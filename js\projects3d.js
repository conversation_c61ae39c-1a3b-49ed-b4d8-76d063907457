// ===== ADVANCED 3D PROJECTS SHOWCASE =====

class Projects3D {
  constructor() {
    this.currentFilter = 'all';
    this.currentView = 'grid';
    this.projects = projectsData;
    this.filteredProjects = [...this.projects];
    this.searchTerm = '';
    
    this.init();
  }

  init() {
    this.setupEventListeners();
    this.renderProjects();
    this.setupFeaturedProject();
    this.updateFilterCounts();
  }

  setupEventListeners() {
    // Filter buttons
    document.querySelectorAll('.filter-btn-3d').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.handleFilterChange(e.target.closest('.filter-btn-3d').dataset.filter);
      });
    });

    // Search input
    const searchInput = document.getElementById('project-search');
    if (searchInput) {
      searchInput.addEventListener('input', (e) => {
        this.handleSearch(e.target.value);
      });
    }

    // View toggle
    document.querySelectorAll('.view-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.handleViewChange(e.target.closest('.view-btn').dataset.view);
      });
    });

    // Modal events
    this.setupModalEvents();

    // Intersection Observer for animations
    this.setupScrollAnimations();
  }

  handleFilterChange(filter) {
    // Update active button
    document.querySelectorAll('.filter-btn-3d').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

    this.currentFilter = filter;
    this.filterProjects();
  }

  handleSearch(term) {
    this.searchTerm = term.toLowerCase();
    this.filterProjects();
  }

  handleViewChange(view) {
    // Update active button
    document.querySelectorAll('.view-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-view="${view}"]`).classList.add('active');

    this.currentView = view;
    this.renderProjects();
  }

  filterProjects() {
    this.filteredProjects = this.projects.filter(project => {
      const matchesFilter = this.currentFilter === 'all' || project.category === this.currentFilter;
      const matchesSearch = this.searchTerm === '' || 
        project.title.toLowerCase().includes(this.searchTerm) ||
        project.description.toLowerCase().includes(this.searchTerm) ||
        project.technologies.some(tech => tech.toLowerCase().includes(this.searchTerm));
      
      return matchesFilter && matchesSearch;
    });

    this.renderProjects();
    this.updateFilterCounts();
  }

  updateFilterCounts() {
    const counts = {
      all: this.projects.length,
      frontend: this.projects.filter(p => p.category === 'frontend').length,
      backend: this.projects.filter(p => p.category === 'backend').length,
      fullstack: this.projects.filter(p => p.category === 'fullstack').length
    };

    Object.keys(counts).forEach(filter => {
      const btn = document.querySelector(`[data-filter="${filter}"]`);
      const countElement = btn?.querySelector('.filter-count');
      if (countElement) {
        countElement.textContent = counts[filter];
      }
    });
  }

  renderProjects() {
    const container = document.getElementById('projects-grid');
    if (!container) return;

    // Add loading state
    container.classList.add('loading');

    setTimeout(() => {
      if (this.currentView === 'grid') {
        this.renderGridView(container);
      } else {
        this.renderListView(container);
      }
      
      container.classList.remove('loading');
      this.animateProjectCards();
    }, 300);
  }

  renderGridView(container) {
    container.className = 'projects-grid-3d';
    container.innerHTML = this.filteredProjects.map((project, index) => `
      <div class="project-card-3d" data-project-id="${project.id}" style="animation-delay: ${index * 0.1}s">
        <div class="project-image-3d">
          <i class="${project.image}"></i>
        </div>
        <div class="project-content-3d">
          <h3 class="project-title-3d">${project.title}</h3>
          <p class="project-description-3d">${project.description}</p>
          <div class="project-tech-3d">
            ${project.technologies.map(tech => `
              <span class="tech-tag-3d">${tech}</span>
            `).join('')}
          </div>
          <div class="project-links-3d">
            <a href="${project.githubUrl}" class="project-link-3d" target="_blank" rel="noopener">
              <i class="fab fa-github"></i>
              <span>Code</span>
            </a>
            ${project.liveUrl ? `
              <a href="${project.liveUrl}" class="project-link-3d" target="_blank" rel="noopener">
                <i class="fas fa-external-link-alt"></i>
                <span>Demo</span>
              </a>
            ` : ''}
            <button class="project-link-3d view-details" data-project-id="${project.id}">
              <i class="fas fa-info-circle"></i>
              <span>Details</span>
            </button>
          </div>
        </div>
      </div>
    `).join('');

    // Add click handlers for details buttons
    container.querySelectorAll('.view-details').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.preventDefault();
        const projectId = parseInt(btn.dataset.projectId);
        this.showProjectModal(projectId);
      });
    });
  }

  renderListView(container) {
    container.className = 'projects-list-view';
    container.innerHTML = this.filteredProjects.map((project, index) => `
      <div class="project-list-item" data-project-id="${project.id}" style="animation-delay: ${index * 0.1}s">
        <div class="list-item-image">
          <i class="${project.image}"></i>
        </div>
        <div class="list-item-content">
          <h3 class="list-item-title">${project.title}</h3>
          <p class="list-item-description">${project.description}</p>
          <div class="list-item-tech">
            ${project.technologies.map(tech => `<span class="tech-tag">${tech}</span>`).join('')}
          </div>
        </div>
        <div class="list-item-actions">
          <a href="${project.githubUrl}" class="btn-list" target="_blank" rel="noopener">
            <i class="fab fa-github"></i>
          </a>
          ${project.liveUrl ? `
            <a href="${project.liveUrl}" class="btn-list" target="_blank" rel="noopener">
              <i class="fas fa-external-link-alt"></i>
            </a>
          ` : ''}
          <button class="btn-list view-details" data-project-id="${project.id}">
            <i class="fas fa-info-circle"></i>
          </button>
        </div>
      </div>
    `).join('');

    // Add click handlers for details buttons
    container.querySelectorAll('.view-details').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.preventDefault();
        const projectId = parseInt(btn.dataset.projectId);
        this.showProjectModal(projectId);
      });
    });
  }

  animateProjectCards() {
    const cards = document.querySelectorAll('.project-card-3d, .project-list-item');
    cards.forEach((card, index) => {
      card.style.opacity = '0';
      card.style.transform = 'translateY(50px)';
      
      setTimeout(() => {
        card.style.transition = 'all 0.6s ease';
        card.style.opacity = '1';
        card.style.transform = 'translateY(0)';
      }, index * 100);
    });
  }

  setupFeaturedProject() {
    const featuredProject = this.projects.find(p => p.featured) || this.projects[0];
    const featuredContainer = document.getElementById('featured-project');
    
    if (featuredContainer && featuredProject) {
      // Update featured project content
      const title = featuredContainer.querySelector('.featured-title');
      const description = featuredContainer.querySelector('.featured-description');
      const techContainer = featuredContainer.querySelector('.featured-tech');
      const codeLink = featuredContainer.querySelector('.btn-featured.primary');
      const demoLink = featuredContainer.querySelector('.btn-featured.secondary');

      if (title) title.textContent = featuredProject.title;
      if (description) description.textContent = featuredProject.description;
      
      if (techContainer) {
        techContainer.innerHTML = featuredProject.technologies.map(tech => 
          `<span class="tech-badge">${tech}</span>`
        ).join('');
      }

      if (codeLink) codeLink.href = featuredProject.githubUrl;
      if (demoLink && featuredProject.liveUrl) {
        demoLink.href = featuredProject.liveUrl;
      } else if (demoLink) {
        demoLink.style.display = 'none';
      }
    }
  }

  setupModalEvents() {
    const modal = document.getElementById('project-modal');
    const closeBtn = document.getElementById('modal-close');
    const overlay = modal?.querySelector('.modal-overlay');

    if (closeBtn) {
      closeBtn.addEventListener('click', () => this.hideProjectModal());
    }

    if (overlay) {
      overlay.addEventListener('click', () => this.hideProjectModal());
    }

    // Close on escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && modal?.classList.contains('active')) {
        this.hideProjectModal();
      }
    });
  }

  showProjectModal(projectId) {
    const project = this.projects.find(p => p.id === projectId);
    if (!project) return;

    const modal = document.getElementById('project-modal');
    if (!modal) return;

    // Update modal content
    const title = modal.querySelector('.modal-title');
    const description = modal.querySelector('.modal-description');
    const techContainer = modal.querySelector('.modal-tech');
    const featuresList = modal.querySelector('.features-list');
    const codeLink = modal.querySelector('.btn-modal.primary');
    const demoLink = modal.querySelector('.btn-modal.secondary');

    if (title) title.textContent = project.title;
    if (description) description.textContent = project.description;
    
    if (techContainer) {
      techContainer.innerHTML = project.technologies.map(tech => 
        `<span class="tech-badge">${tech}</span>`
      ).join('');
    }

    // Add sample features (you can extend the project data to include these)
    const features = this.getProjectFeatures(project);
    if (featuresList) {
      featuresList.innerHTML = features.map(feature => 
        `<li>${feature}</li>`
      ).join('');
    }

    if (codeLink) codeLink.href = project.githubUrl;
    if (demoLink && project.liveUrl) {
      demoLink.href = project.liveUrl;
      demoLink.style.display = 'inline-flex';
    } else if (demoLink) {
      demoLink.style.display = 'none';
    }

    // Show modal
    modal.classList.add('active');
    document.body.style.overflow = 'hidden';
  }

  hideProjectModal() {
    const modal = document.getElementById('project-modal');
    if (modal) {
      modal.classList.remove('active');
      document.body.style.overflow = '';
    }
  }

  getProjectFeatures(project) {
    // Sample features based on project type
    const featureMap = {
      'E-Commerce Platform': [
        'User authentication and authorization',
        'Product catalog with search and filtering',
        'Shopping cart and checkout process',
        'Payment integration with Stripe',
        'Admin dashboard for inventory management',
        'Real-time inventory updates'
      ],
      'Task Management App': [
        'Drag and drop task organization',
        'Real-time collaboration features',
        'Team member management',
        'Progress tracking and analytics',
        'File attachments and comments',
        'Mobile responsive design'
      ],
      'Weather Dashboard': [
        'Current weather conditions',
        '7-day weather forecast',
        'Interactive weather maps',
        'Location-based weather data',
        'Weather alerts and notifications',
        'Historical weather data'
      ]
    };

    return featureMap[project.title] || [
      'Modern responsive design',
      'Clean and intuitive user interface',
      'Optimized performance',
      'Cross-browser compatibility',
      'Mobile-first approach'
    ];
  }

  setupScrollAnimations() {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate');
        }
      });
    }, { threshold: 0.1 });

    // Observe project cards for scroll animations
    const observeCards = () => {
      document.querySelectorAll('.project-card-3d, .project-list-item').forEach(card => {
        observer.observe(card);
      });
    };

    // Initial observation
    setTimeout(observeCards, 100);

    // Re-observe after filtering
    const originalRenderProjects = this.renderProjects.bind(this);
    this.renderProjects = function() {
      originalRenderProjects();
      setTimeout(observeCards, 100);
    };
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new Projects3D();
});
