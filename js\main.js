// ===== MAIN APPLICATION LOGIC =====

class PortfolioApp {
  constructor() {
    this.currentFilter = 'all';
    this.isMenuOpen = false;
    this.init();
  }

  init() {
    this.setupEventListeners();
    this.renderSkills();
    this.renderProjects();
    this.setupNavigation();
    this.setupContactForm();
    this.setupMobileMenu();
    this.initializeAnimations();
  }

  // Setup all event listeners
  setupEventListeners() {
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', (e) => {
        e.preventDefault();
        const target = anchor.getAttribute('href');
        this.smoothScrollTo(target);
        this.closeMobileMenu();
      });
    });

    // Project filter buttons
    document.querySelectorAll('.filter-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.filterProjects(e.target.dataset.filter);
      });
    });

    // Window resize handler
    window.addEventListener('resize', this.debounce(() => {
      this.handleResize();
    }, 250));

    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.closeMobileMenu();
      }
    });
  }

  // Render skills section
  renderSkills() {
    const skillsCategories = document.querySelectorAll('.skills-category');
    
    skillsCategories.forEach((category, index) => {
      const categoryName = ['frontend', 'backend', 'tools'][index];
      const skillsGrid = category.querySelector('.skills-grid');
      const skills = skillsData[categoryName];

      if (skills && skillsGrid) {
        skillsGrid.innerHTML = skills.map(skill => `
          <div class="skill-item fade-in" style="animation-delay: ${index * 0.1}s">
            <div class="skill-icon">
              <i class="${skill.icon}"></i>
            </div>
            <div class="skill-name">${skill.name}</div>
            <div class="skill-level">${skill.level}</div>
          </div>
        `).join('');
      }
    });
  }

  // Render projects section
  renderProjects() {
    const projectsGrid = document.getElementById('projects-grid');
    if (!projectsGrid) return;

    projectsGrid.innerHTML = projectsData.map(project => `
      <div class="project-card fade-in" data-category="${project.category}">
        <div class="project-image">
          <i class="${project.image}"></i>
        </div>
        <div class="project-content">
          <h3 class="project-title">${project.title}</h3>
          <p class="project-description">${project.description}</p>
          <div class="project-tech">
            ${project.technologies.map(tech => `
              <span class="tech-tag">${tech}</span>
            `).join('')}
          </div>
          <div class="project-links">
            <a href="${project.githubUrl}" class="project-link" target="_blank" rel="noopener">
              <i class="fab fa-github"></i>
              <span>Code</span>
            </a>
            ${project.liveUrl ? `
              <a href="${project.liveUrl}" class="project-link" target="_blank" rel="noopener">
                <i class="fas fa-external-link-alt"></i>
                <span>Live Demo</span>
              </a>
            ` : ''}
          </div>
        </div>
      </div>
    `).join('');
  }

  // Filter projects by category
  filterProjects(filter) {
    this.currentFilter = filter;
    
    // Update active filter button
    document.querySelectorAll('.filter-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

    // Filter project cards
    const projectCards = document.querySelectorAll('.project-card');
    projectCards.forEach(card => {
      const category = card.dataset.category;
      
      if (filter === 'all' || category === filter) {
        card.style.display = 'block';
        card.style.animation = 'fadeInUp 0.6s ease-out forwards';
      } else {
        card.style.display = 'none';
      }
    });
  }

  // Setup navigation highlighting
  setupNavigation() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');

    const highlightNavigation = () => {
      const scrollY = window.pageYOffset;

      sections.forEach(section => {
        const sectionHeight = section.offsetHeight;
        const sectionTop = section.offsetTop - 100;
        const sectionId = section.getAttribute('id');

        if (scrollY > sectionTop && scrollY <= sectionTop + sectionHeight) {
          navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${sectionId}`) {
              link.classList.add('active');
            }
          });
        }
      });
    };

    window.addEventListener('scroll', this.throttle(highlightNavigation, 100));
  }

  // Setup contact form
  setupContactForm() {
    const contactForm = document.getElementById('contact-form');
    if (!contactForm) return;

    contactForm.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleContactForm(contactForm);
    });
  }

  // Handle contact form submission
  handleContactForm(form) {
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Sending...';
    submitBtn.disabled = true;

    // Simulate form submission (replace with actual form handling)
    setTimeout(() => {
      this.showNotification('Message sent successfully!', 'success');
      form.reset();
      submitBtn.textContent = originalText;
      submitBtn.disabled = false;
    }, 2000);
  }

  // Setup mobile menu
  setupMobileMenu() {
    const mobileMenuBtn = document.getElementById('mobile-menu');
    const navMenu = document.getElementById('nav-menu');

    if (mobileMenuBtn && navMenu) {
      mobileMenuBtn.addEventListener('click', () => {
        this.toggleMobileMenu();
      });

      // Close menu when clicking outside
      document.addEventListener('click', (e) => {
        if (!mobileMenuBtn.contains(e.target) && !navMenu.contains(e.target)) {
          this.closeMobileMenu();
        }
      });
    }
  }

  // Toggle mobile menu
  toggleMobileMenu() {
    const mobileMenuBtn = document.getElementById('mobile-menu');
    const navMenu = document.getElementById('nav-menu');

    this.isMenuOpen = !this.isMenuOpen;
    
    mobileMenuBtn.classList.toggle('active');
    navMenu.classList.toggle('active');
    
    // Prevent body scroll when menu is open
    document.body.style.overflow = this.isMenuOpen ? 'hidden' : '';
  }

  // Close mobile menu
  closeMobileMenu() {
    const mobileMenuBtn = document.getElementById('mobile-menu');
    const navMenu = document.getElementById('nav-menu');

    this.isMenuOpen = false;
    
    if (mobileMenuBtn) mobileMenuBtn.classList.remove('active');
    if (navMenu) navMenu.classList.remove('active');
    
    document.body.style.overflow = '';
  }

  // Initialize animations
  initializeAnimations() {
    // Add fade-in class to elements that should animate on scroll
    const animateElements = document.querySelectorAll(
      '.hero-content, .about-content, .skills-category, .project-card, .contact-content'
    );
    
    animateElements.forEach(el => {
      el.classList.add('fade-in');
    });
  }

  // Smooth scroll to target
  smoothScrollTo(target) {
    const targetElement = document.querySelector(target);
    if (!targetElement) return;

    const targetPosition = targetElement.offsetTop - 70;
    
    window.scrollTo({
      top: targetPosition,
      behavior: 'smooth'
    });
  }

  // Show notification
  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${type === 'success' ? '#10B981' : '#3B82F6'};
      color: white;
      padding: 1rem 1.5rem;
      border-radius: 0.5rem;
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      z-index: 1000;
      animation: slideInRight 0.3s ease-out;
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.style.animation = 'slideOutRight 0.3s ease-out';
      setTimeout(() => {
        notification.remove();
      }, 300);
    }, 3000);
  }

  // Handle window resize
  handleResize() {
    // Close mobile menu on resize to desktop
    if (window.innerWidth > 768 && this.isMenuOpen) {
      this.closeMobileMenu();
    }
  }

  // Utility: Debounce function
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  // Utility: Throttle function
  throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
}

// Add notification animations CSS
const notificationCSS = `
  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slideOutRight {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }
`;

const style = document.createElement('style');
style.textContent = notificationCSS;
document.head.appendChild(style);

// Initialize the application
let portfolioApp;

if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    portfolioApp = new PortfolioApp();
  });
} else {
  portfolioApp = new PortfolioApp();
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PortfolioApp;
}
