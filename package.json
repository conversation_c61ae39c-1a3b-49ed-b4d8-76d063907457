{"name": "grady-rankin-portfolio", "version": "1.0.0", "description": "Modern portfolio website for <PERSON> - Software Engineer", "main": "index.html", "scripts": {"start": "npx live-server --port=3000 --open=/", "build": "echo 'No build process needed for static site'", "test": "echo 'No tests specified'", "validate": "npx html-validate index.html", "optimize": "npx imagemin assets/**/*.{jpg,png,gif} --out-dir=assets/optimized", "serve": "npx http-server -p 3000 -o"}, "keywords": ["portfolio", "software-engineer", "web-development", "frontend", "backend", "javascript", "html", "css", "responsive"], "author": "<PERSON>in <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/grady-rankin/portfolio.git"}, "homepage": "https://grady-rankin.netlify.app", "devDependencies": {"live-server": "^1.2.2", "http-server": "^14.1.1", "html-validate": "^8.7.4", "imagemin": "^8.0.1", "imagemin-cli": "^7.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "engines": {"node": ">=14.0.0"}}