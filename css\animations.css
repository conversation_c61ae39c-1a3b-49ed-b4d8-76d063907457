/* ===== ADVANCED ANIMATIONS & KEYFRAMES ===== */

/* Hero Animations */
@keyframes glitch {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(-2px, 2px); }
  40% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  80% { transform: translate(2px, -2px); }
}

@keyframes glitch-1 {
  0%, 100% { transform: translate(0); }
  10% { transform: translate(-2px, -2px); }
  20% { transform: translate(2px, 2px); }
  30% { transform: translate(-2px, 2px); }
  40% { transform: translate(2px, -2px); }
  50% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  70% { transform: translate(-2px, 2px); }
  80% { transform: translate(2px, -2px); }
  90% { transform: translate(-2px, -2px); }
}

@keyframes glitch-2 {
  0%, 100% { transform: translate(0); }
  15% { transform: translate(2px, 2px); }
  25% { transform: translate(-2px, -2px); }
  35% { transform: translate(2px, -2px); }
  45% { transform: translate(-2px, 2px); }
  55% { transform: translate(2px, 2px); }
  65% { transform: translate(-2px, -2px); }
  75% { transform: translate(2px, -2px); }
  85% { transform: translate(-2px, 2px); }
  95% { transform: translate(2px, 2px); }
}

@keyframes letterFloat {
  0%, 100% {
    transform: translateY(0px) rotateZ(0deg);
  }
  25% {
    transform: translateY(-10px) rotateZ(1deg);
  }
  50% {
    transform: translateY(-5px) rotateZ(-1deg);
  }
  75% {
    transform: translateY(-15px) rotateZ(0.5deg);
  }
}

@keyframes floatTech {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(120deg);
  }
  66% {
    transform: translateY(-10px) rotate(240deg);
  }
}

@keyframes scrollBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Loading Animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(212, 175, 55, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.6);
  }
}

@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* Code Animation for Hero Section */
@keyframes codeFlow {
  0% {
    background: linear-gradient(90deg, 
      var(--color-secondary) 0%, 
      var(--color-secondary) 100%);
  }
  25% {
    background: linear-gradient(90deg, 
      var(--color-primary) 0%, 
      var(--color-secondary) 50%, 
      var(--color-secondary) 100%);
  }
  50% {
    background: linear-gradient(90deg, 
      var(--color-secondary) 0%, 
      var(--color-primary) 50%, 
      var(--color-secondary) 100%);
  }
  75% {
    background: linear-gradient(90deg, 
      var(--color-secondary) 0%, 
      var(--color-secondary) 50%, 
      var(--color-primary) 100%);
  }
  100% {
    background: linear-gradient(90deg, 
      var(--color-secondary) 0%, 
      var(--color-secondary) 100%);
  }
}

/* Skill Item Hover Animation */
@keyframes skillPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 10px 15px -3px rgba(212, 175, 55, 0.2);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
}

/* Project Card Hover Animation */
@keyframes projectFloat {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-5px) scale(1.02);
  }
}

/* Button Ripple Effect */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* Navigation Slide Animation */
@keyframes slideDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

/* ===== ANIMATION CLASSES ===== */

/* Entrance Animations */
.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-fadeInLeft {
  animation: fadeInLeft 0.6s ease-out forwards;
}

.animate-fadeInRight {
  animation: fadeInRight 0.6s ease-out forwards;
}

.animate-fadeIn {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-slideInDown {
  animation: slideInDown 0.6s ease-out forwards;
}

/* Continuous Animations */
.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Code Animation */
.code-line {
  animation: codeFlow 3s ease-in-out infinite;
  animation-delay: var(--delay, 0s);
}

.code-line:nth-child(1) {
  --delay: 0s;
}

.code-line:nth-child(2) {
  --delay: 0.5s;
}

.code-line:nth-child(3) {
  --delay: 1s;
}

/* Skill Animation */
.skill-item:hover {
  animation: skillPulse 0.3s ease-in-out;
}

/* Project Animation */
.project-card:hover {
  animation: projectFloat 0.3s ease-in-out;
}

/* Button Ripple Effect */
.btn {
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn:active::before {
  width: 300px;
  height: 300px;
}

/* Scroll Animations */
.scroll-animate {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.6s ease-out;
}

.scroll-animate.animate {
  opacity: 1;
  transform: translateY(0);
}

/* Stagger Animation Delays */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }
.stagger-6 { animation-delay: 0.6s; }

/* Loading States */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(212, 175, 55, 0.2),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Advanced Project Animations */
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(50px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes projectCardFloat {
  0%, 100% {
    transform: translateY(0) rotateX(0deg) rotateY(0deg);
  }
  50% {
    transform: translateY(-5px) rotateX(2deg) rotateY(2deg);
  }
}

@keyframes techBadgeGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(212, 175, 55, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.6);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 3D Transform Animations */
@keyframes rotate3D {
  0% {
    transform: rotateX(0deg) rotateY(0deg);
  }
  25% {
    transform: rotateX(5deg) rotateY(5deg);
  }
  50% {
    transform: rotateX(0deg) rotateY(10deg);
  }
  75% {
    transform: rotateX(-5deg) rotateY(5deg);
  }
  100% {
    transform: rotateX(0deg) rotateY(0deg);
  }
}

@keyframes cardFlip {
  0% {
    transform: rotateY(0deg);
  }
  50% {
    transform: rotateY(90deg);
  }
  100% {
    transform: rotateY(0deg);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Particle Effects */
@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(120deg);
  }
  66% {
    transform: translateY(-10px) rotate(240deg);
  }
}

@keyframes particleFade {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
}

/* Loading States */
@keyframes skeletonLoading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes progressBar {
  from {
    width: 0%;
  }
  to {
    width: var(--progress-width, 100%);
  }
}

/* Micro-interactions */
@keyframes buttonPress {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes iconBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes textGlow {
  0%, 100% {
    text-shadow: 0 0 5px rgba(212, 175, 55, 0.5);
  }
  50% {
    text-shadow: 0 0 20px rgba(212, 175, 55, 0.8);
  }
}

/* Hover Effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Text Animations */
.typewriter {
  overflow: hidden;
  border-right: 2px solid var(--color-primary);
  white-space: nowrap;
  animation: typewriter 3s steps(40) 1s forwards, blink 1s infinite;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .scroll-animate {
    opacity: 1;
    transform: none;
  }
}
