# <PERSON> Rankin - Software Engineer <PERSON><PERSON><PERSON>

A modern, responsive portfolio website showcasing full-stack development skills and projects. Built with vanilla HTML, CSS, and JavaScript with a focus on performance, accessibility, and user experience.

## 🎨 Design Features

- **Modern Design**: Clean, professional layout with unique visual elements
- **Color Scheme**: Medium grey, charcoal black, and muted golden yellow
- **Responsive**: Fully responsive design for desktop, tablet, and mobile
- **Animations**: Smooth transitions and scroll-based animations
- **Performance**: Optimized for fast loading and smooth interactions

## 🚀 Technologies Used

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Styling**: CSS Custom Properties, Flexbox, CSS Grid
- **Icons**: Font Awesome 6
- **Fonts**: Inter (primary), JetBrains Mono (code)
- **Animations**: CSS animations with JavaScript intersection observers

## 📁 Project Structure

```
portfolio/
├── index.html              # Main HTML file
├── css/
│   ├── styles.css          # Main stylesheet
│   └── animations.css      # Animation definitions
├── js/
│   ├── main.js            # Main application logic
│   ├── animations.js      # Animation management
│   └── data.js            # Portfolio data and configuration
├── assets/
│   ├── README.md          # Assets documentation
│   └── (add your images, resume, favicon here)
└── README.md              # This file
```

## 🛠️ Setup and Customization

### 1. Clone or Download
Download the project files to your local machine.

### 2. Customize Content
Edit the data in `js/data.js` to reflect your information:

- **Skills**: Update the `skillsData` object with your technical skills
- **Projects**: Modify the `projectsData` array with your projects
- **Contact**: Update `contactInfo` with your contact details
- **Personal Info**: Update the hero section and about section in `index.html`

### 3. Add Your Assets
- Add your profile photo to the `assets/` directory
- Include project screenshots
- Add your resume PDF
- Replace the favicon with your custom icon

### 4. Update Links
- Replace placeholder GitHub, LinkedIn, and other social media links
- Update project repository and live demo URLs
- Add your actual email address and contact information

### 5. Deploy
The website is built with vanilla technologies and can be deployed to any static hosting service:

- **Netlify**: Drag and drop the folder or connect to Git
- **Vercel**: Import the project from GitHub
- **GitHub Pages**: Push to a GitHub repository and enable Pages
- **Traditional Hosting**: Upload files via FTP

## 📱 Responsive Breakpoints

- **Mobile**: 480px and below
- **Tablet**: 768px and below
- **Desktop**: 1024px and above
- **Wide**: 1200px and above

## 🎯 Key Features

### Navigation
- Fixed navigation with smooth scrolling
- Active section highlighting
- Mobile-friendly hamburger menu

### Hero Section
- Animated introduction
- Call-to-action buttons
- Scroll indicator

### Skills Section
- Categorized skill display (Frontend, Backend, Tools)
- Hover animations
- Skill level indicators

### Projects Section
- Filterable project grid
- Project cards with hover effects
- GitHub and live demo links
- Technology tags

### Contact Section
- Contact form with validation
- Social media links
- Professional contact information

### Performance Features
- Intersection Observer for scroll animations
- Debounced scroll events
- Optimized CSS with custom properties
- Minimal JavaScript bundle

## 🔧 Customization Guide

### Colors
Update the CSS custom properties in `css/styles.css`:

```css
:root {
  --color-primary: #D4AF37;        /* Golden yellow */
  --color-secondary: #6B7280;      /* Medium grey */
  --color-dark: #1F2937;           /* Charcoal black */
  /* ... other colors */
}
```

### Typography
Change fonts by updating the Google Fonts link in `index.html` and the CSS variables:

```css
:root {
  --font-primary: 'Your-Font', sans-serif;
  --font-mono: 'Your-Mono-Font', monospace;
}
```

### Animations
Modify animation timing and effects in `css/animations.css` and `js/animations.js`.

## 📈 SEO and Performance

- Semantic HTML structure
- Meta tags for social sharing (Open Graph, Twitter Cards)
- Optimized images and assets
- Accessible design with proper ARIA labels
- Fast loading with minimal dependencies

## 🌐 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Feel free to fork this project and customize it for your own portfolio. If you make improvements that could benefit others, pull requests are welcome!

## 📞 Contact

If you have questions about this portfolio template, feel free to reach out:

- **Email**: <EMAIL>
- **LinkedIn**: [linkedin.com/in/grady-rankin](https://linkedin.com/in/grady-rankin)
- **GitHub**: [github.com/grady-rankin](https://github.com/grady-rankin)

---

**Note**: Remember to update all placeholder content with your actual information before deploying your portfolio!
