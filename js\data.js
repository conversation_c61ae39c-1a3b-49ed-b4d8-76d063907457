// ===== PORTFOLIO DATA =====

// Skills Data
const skillsData = {
  frontend: [
    {
      name: 'JavaScript',
      icon: 'fab fa-js-square',
      level: 'Advanced'
    },
    {
      name: 'React',
      icon: 'fab fa-react',
      level: 'Advanced'
    },
    {
      name: 'Vue.js',
      icon: 'fab fa-vuejs',
      level: 'Intermediate'
    },
    {
      name: 'HTML5',
      icon: 'fab fa-html5',
      level: 'Expert'
    },
    {
      name: 'CSS3',
      icon: 'fab fa-css3-alt',
      level: 'Expert'
    },
    {
      name: 'TypeScript',
      icon: 'fas fa-code',
      level: 'Intermediate'
    },
    {
      name: 'Sass/SCSS',
      icon: 'fab fa-sass',
      level: 'Advanced'
    },
    {
      name: 'Bootstrap',
      icon: 'fab fa-bootstrap',
      level: 'Advanced'
    }
  ],
  backend: [
    {
      name: 'Node.js',
      icon: 'fab fa-node-js',
      level: 'Advanced'
    },
    {
      name: 'Python',
      icon: 'fab fa-python',
      level: 'Advanced'
    },
    {
      name: 'Express.js',
      icon: 'fas fa-server',
      level: 'Advanced'
    },
    {
      name: 'Django',
      icon: 'fas fa-code',
      level: 'Intermediate'
    },
    {
      name: 'MongoDB',
      icon: 'fas fa-database',
      level: 'Intermediate'
    },
    {
      name: 'PostgreSQL',
      icon: 'fas fa-database',
      level: 'Intermediate'
    },
    {
      name: 'REST APIs',
      icon: 'fas fa-exchange-alt',
      level: 'Advanced'
    },
    {
      name: 'GraphQL',
      icon: 'fas fa-project-diagram',
      level: 'Beginner'
    }
  ],
  tools: [
    {
      name: 'Git',
      icon: 'fab fa-git-alt',
      level: 'Advanced'
    },
    {
      name: 'GitHub',
      icon: 'fab fa-github',
      level: 'Advanced'
    },
    {
      name: 'VS Code',
      icon: 'fas fa-code',
      level: 'Expert'
    },
    {
      name: 'Docker',
      icon: 'fab fa-docker',
      level: 'Beginner'
    },
    {
      name: 'AWS',
      icon: 'fab fa-aws',
      level: 'Beginner'
    },
    {
      name: 'Webpack',
      icon: 'fas fa-cube',
      level: 'Intermediate'
    },
    {
      name: 'NPM',
      icon: 'fab fa-npm',
      level: 'Advanced'
    },
    {
      name: 'Figma',
      icon: 'fab fa-figma',
      level: 'Intermediate'
    }
  ]
};

// Projects Data
const projectsData = [
  {
    id: 1,
    title: 'E-Commerce Platform',
    description: 'A full-stack e-commerce solution with user authentication, product management, shopping cart, and payment integration. Features include real-time inventory updates and admin dashboard.',
    image: 'fas fa-shopping-cart',
    technologies: ['React', 'Node.js', 'MongoDB', 'Stripe API', 'JWT'],
    category: 'fullstack',
    githubUrl: 'https://github.com/yourusername/ecommerce-platform',
    liveUrl: 'https://your-ecommerce-demo.netlify.app',
    featured: true
  },
  {
    id: 2,
    title: 'Task Management App',
    description: 'A collaborative task management application with drag-and-drop functionality, real-time updates, and team collaboration features. Built with modern React patterns and state management.',
    image: 'fas fa-tasks',
    technologies: ['React', 'Redux', 'Firebase', 'Material-UI', 'Socket.io'],
    category: 'frontend',
    githubUrl: 'https://github.com/yourusername/task-manager',
    liveUrl: 'https://your-taskmanager-demo.netlify.app',
    featured: true
  },
  {
    id: 3,
    title: 'Weather Dashboard',
    description: 'A responsive weather application that provides current conditions, forecasts, and weather maps. Features location-based weather data and beautiful data visualizations.',
    image: 'fas fa-cloud-sun',
    technologies: ['JavaScript', 'Chart.js', 'OpenWeather API', 'CSS3'],
    category: 'frontend',
    githubUrl: 'https://github.com/yourusername/weather-dashboard',
    liveUrl: 'https://your-weather-demo.netlify.app',
    featured: false
  },
  {
    id: 4,
    title: 'Blog API',
    description: 'A RESTful API for a blogging platform with user authentication, CRUD operations, comment system, and image upload functionality. Includes comprehensive API documentation.',
    image: 'fas fa-blog',
    technologies: ['Node.js', 'Express', 'MongoDB', 'JWT', 'Multer'],
    category: 'backend',
    githubUrl: 'https://github.com/yourusername/blog-api',
    liveUrl: null,
    featured: true
  },
  {
    id: 5,
    title: 'Portfolio Website',
    description: 'A modern, responsive portfolio website showcasing projects and skills. Features smooth animations, dark mode toggle, and optimized performance.',
    image: 'fas fa-user',
    technologies: ['HTML5', 'CSS3', 'JavaScript', 'GSAP'],
    category: 'frontend',
    githubUrl: 'https://github.com/yourusername/portfolio',
    liveUrl: 'https://your-portfolio.netlify.app',
    featured: false
  },
  {
    id: 6,
    title: 'Chat Application',
    description: 'Real-time chat application with multiple rooms, private messaging, file sharing, and emoji support. Built with Socket.io for instant communication.',
    image: 'fas fa-comments',
    technologies: ['React', 'Node.js', 'Socket.io', 'MongoDB', 'Express'],
    category: 'fullstack',
    githubUrl: 'https://github.com/yourusername/chat-app',
    liveUrl: 'https://your-chat-demo.herokuapp.com',
    featured: false
  }
];

// Contact Information
const contactInfo = {
  email: '<EMAIL>',
  phone: '+****************',
  location: 'Your City, State',
  linkedin: 'https://linkedin.com/in/grady-rankin',
  github: 'https://github.com/grady-rankin',
  twitter: 'https://twitter.com/grady_rankin',
  resume: 'assets/grady-rankin-resume.pdf'
};

// Navigation Links
const navigationLinks = [
  { name: 'Home', href: '#home' },
  { name: 'About', href: '#about' },
  { name: 'Skills', href: '#skills' },
  { name: 'Projects', href: '#projects' },
  { name: 'Contact', href: '#contact' }
];

// Animation Configuration
const animationConfig = {
  observerOptions: {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  },
  staggerDelay: 100,
  animationDuration: 600
};

// Theme Configuration
const themeConfig = {
  colors: {
    primary: '#D4AF37',
    primaryLight: '#E6C55A',
    primaryDark: '#B8941F',
    secondary: '#6B7280',
    secondaryLight: '#9CA3AF',
    secondaryDark: '#4B5563',
    dark: '#1F2937',
    darkLight: '#374151',
    darker: '#111827'
  },
  breakpoints: {
    mobile: '480px',
    tablet: '768px',
    desktop: '1024px',
    wide: '1200px'
  }
};

// Export data for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    skillsData,
    projectsData,
    contactInfo,
    navigationLinks,
    animationConfig,
    themeConfig
  };
}
