<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<PERSON>in - Software Engineer Portfolio. Showcasing full-stack development skills, projects, and professional experience.">
    <meta name="keywords" content="software engineer, full-stack developer, web development, portfolio, JavaScript, Python, React">
    <meta name="author" content="Grady Rankin">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Grady Rankin - Software Engineer Portfolio">
    <meta property="og:description" content="Professional portfolio showcasing full-stack development skills and projects">
    <meta property="og:type" content="website">
    <meta property="og:url" content="">
    <meta property="og:image" content="">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<PERSON> Rankin - Software Engineer Portfolio">
    <meta name="twitter:description" content="Professional portfolio showcasing full-stack development skills and projects">
    
    <title><PERSON> - Software Engineer</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">

    <!-- Preload critical resources -->
    <link rel="preload" href="css/styles.css" as="style">
    <link rel="preload" href="js/main.js" as="script">
    
    <!-- Styles -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="#home">GR</a>
            </div>
            <ul class="nav-menu" id="nav-menu">
                <li class="nav-item">
                    <a href="#home" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="#about" class="nav-link">About</a>
                </li>
                <li class="nav-item">
                    <a href="#skills" class="nav-link">Skills</a>
                </li>
                <li class="nav-item">
                    <a href="#projects" class="nav-link">Projects</a>
                </li>
                <li class="nav-item">
                    <a href="#contact" class="nav-link">Contact</a>
                </li>
            </ul>
            <div class="nav-toggle" id="mobile-menu">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="hero-greeting">Hello, I'm</span>
                    <span class="hero-name">Grady Rankin</span>
                    <span class="hero-role">Software Engineer</span>
                </h1>
                <p class="hero-description">
                    I craft elegant solutions to complex problems, specializing in full-stack development 
                    with a passion for clean code and innovative user experiences.
                </p>
                <div class="hero-buttons">
                    <a href="#projects" class="btn btn-primary">View My Work</a>
                    <a href="#contact" class="btn btn-secondary">Get In Touch</a>
                </div>
            </div>
            <div class="hero-visual">
                <div class="code-animation">
                    <div class="code-line"></div>
                    <div class="code-line"></div>
                    <div class="code-line"></div>
                </div>
            </div>
        </div>
        <div class="scroll-indicator">
            <div class="scroll-arrow"></div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about">
        <div class="container">
            <h2 class="section-title">About Me</h2>
            <div class="about-content">
                <div class="about-text">
                    <p class="about-intro">
                        I'm a passionate software engineer with a strong foundation in both frontend and backend development. 
                        My journey in technology is driven by curiosity and a commitment to continuous learning.
                    </p>
                    <p>
                        With experience in modern web technologies and a keen eye for user experience, I enjoy building 
                        applications that are not only functional but also intuitive and engaging. I believe in writing 
                        clean, maintainable code and following best practices in software development.
                    </p>
                    <div class="about-highlights">
                        <div class="highlight-item">
                            <i class="fas fa-code"></i>
                            <span>Clean Code Advocate</span>
                        </div>
                        <div class="highlight-item">
                            <i class="fas fa-lightbulb"></i>
                            <span>Problem Solver</span>
                        </div>
                        <div class="highlight-item">
                            <i class="fas fa-users"></i>
                            <span>Team Collaborator</span>
                        </div>
                    </div>
                </div>
                <div class="about-image">
                    <div class="image-placeholder">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="skills">
        <div class="container">
            <h2 class="section-title">Technical Skills</h2>
            <div class="skills-content">
                <div class="skills-category">
                    <h3 class="category-title">Frontend Development</h3>
                    <div class="skills-grid">
                        <!-- Skills will be populated by JavaScript -->
                    </div>
                </div>
                <div class="skills-category">
                    <h3 class="category-title">Backend Development</h3>
                    <div class="skills-grid">
                        <!-- Skills will be populated by JavaScript -->
                    </div>
                </div>
                <div class="skills-category">
                    <h3 class="category-title">Tools & Technologies</h3>
                    <div class="skills-grid">
                        <!-- Skills will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="projects">
        <div class="container">
            <h2 class="section-title">Featured Projects</h2>
            <div class="projects-filter">
                <button class="filter-btn active" data-filter="all">All</button>
                <button class="filter-btn" data-filter="frontend">Frontend</button>
                <button class="filter-btn" data-filter="backend">Backend</button>
                <button class="filter-btn" data-filter="fullstack">Full Stack</button>
            </div>
            <div class="projects-grid" id="projects-grid">
                <!-- Projects will be populated by JavaScript -->
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="container">
            <h2 class="section-title">Let's Connect</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <p class="contact-description">
                        I'm always interested in new opportunities and collaborations. 
                        Feel free to reach out if you'd like to discuss a project or just say hello!
                    </p>
                    <div class="contact-methods">
                        <a href="mailto:<EMAIL>" class="contact-method">
                            <i class="fas fa-envelope"></i>
                            <span><EMAIL></span>
                        </a>
                        <a href="https://linkedin.com/in/yourprofile" class="contact-method" target="_blank">
                            <i class="fab fa-linkedin"></i>
                            <span>LinkedIn Profile</span>
                        </a>
                        <a href="https://github.com/yourusername" class="contact-method" target="_blank">
                            <i class="fab fa-github"></i>
                            <span>GitHub Profile</span>
                        </a>
                    </div>
                </div>
                <div class="contact-form">
                    <form id="contact-form">
                        <div class="form-group">
                            <input type="text" id="name" name="name" placeholder="Your Name" required>
                        </div>
                        <div class="form-group">
                            <input type="email" id="email" name="email" placeholder="Your Email" required>
                        </div>
                        <div class="form-group">
                            <textarea id="message" name="message" placeholder="Your Message" rows="5" required></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">Send Message</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p>&copy; 2025 Grady Rankin. All rights reserved.</p>
                <div class="footer-links">
                    <a href="#home">Home</a>
                    <a href="#about">About</a>
                    <a href="#projects">Projects</a>
                    <a href="#contact">Contact</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/data.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
