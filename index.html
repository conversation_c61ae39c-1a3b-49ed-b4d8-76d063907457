<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<PERSON>in - Software Engineer Portfolio. Showcasing full-stack development skills, projects, and professional experience.">
    <meta name="keywords" content="software engineer, full-stack developer, web development, portfolio, JavaScript, Python, React">
    <meta name="author" content="Grady Rankin">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Grady Rankin - Software Engineer Portfolio">
    <meta property="og:description" content="Professional portfolio showcasing full-stack development skills and projects">
    <meta property="og:type" content="website">
    <meta property="og:url" content="">
    <meta property="og:image" content="">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<PERSON> Rankin - Software Engineer Portfolio">
    <meta name="twitter:description" content="Professional portfolio showcasing full-stack development skills and projects">
    
    <title><PERSON> - Software Engineer</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">

    <!-- Preload critical resources -->
    <link rel="preload" href="css/styles.css" as="style">
    <link rel="preload" href="js/main.js" as="script">
    
    <!-- Styles -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="#home">GR</a>
            </div>
            <ul class="nav-menu" id="nav-menu">
                <li class="nav-item">
                    <a href="#home" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="#about" class="nav-link">About</a>
                </li>
                <li class="nav-item">
                    <a href="#skills" class="nav-link">Skills</a>
                </li>
                <li class="nav-item">
                    <a href="#projects" class="nav-link">Projects</a>
                </li>
                <li class="nav-item">
                    <a href="#contact" class="nav-link">Contact</a>
                </li>
                <li class="nav-item">
                    <button class="theme-toggle" id="theme-toggle" aria-label="Toggle theme">
                        <span class="theme-icon sun">☀️</span>
                        <span class="theme-icon moon">🌙</span>
                    </button>
                </li>
            </ul>
            <div class="nav-toggle" id="mobile-menu">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <!-- Particle Background -->
        <canvas id="particle-canvas" class="particle-canvas"></canvas>

        <!-- Geometric Background -->
        <div class="geometric-bg">
            <div class="geometric-shape shape-1"></div>
            <div class="geometric-shape shape-2"></div>
            <div class="geometric-shape shape-3"></div>
            <div class="geometric-shape shape-4"></div>
        </div>

        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-intro">
                    <span class="hero-greeting glitch" data-text="Hello, I'm">Hello, I'm</span>
                </div>

                <h1 class="hero-title">
                    <span class="hero-name">
                        <span class="letter">G</span>
                        <span class="letter">r</span>
                        <span class="letter">a</span>
                        <span class="letter">d</span>
                        <span class="letter">y</span>
                        <span class="space"> </span>
                        <span class="letter">R</span>
                        <span class="letter">a</span>
                        <span class="letter">n</span>
                        <span class="letter">k</span>
                        <span class="letter">i</span>
                        <span class="letter">n</span>
                    </span>
                </h1>

                <div class="hero-role-container">
                    <span class="hero-role-prefix">I'm a </span>
                    <span class="hero-role typewriter" id="typewriter-text"></span>
                    <span class="cursor">|</span>
                </div>

                <p class="hero-description">
                    <span class="description-line">Crafting elegant solutions to complex problems,</span>
                    <span class="description-line">specializing in full-stack development with</span>
                    <span class="description-line">a passion for clean code and innovation.</span>
                </p>

                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number" data-target="50">0</span>
                        <span class="stat-label">Projects</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" data-target="3">0</span>
                        <span class="stat-label">Years Experience</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" data-target="15">0</span>
                        <span class="stat-label">Technologies</span>
                    </div>
                </div>

                <div class="hero-buttons">
                    <a href="#projects" class="btn btn-primary btn-3d">
                        <span class="btn-text">View My Work</span>
                        <span class="btn-icon">→</span>
                    </a>
                    <a href="#contact" class="btn btn-secondary btn-3d">
                        <span class="btn-text">Get In Touch</span>
                        <span class="btn-icon">✉</span>
                    </a>
                </div>
            </div>

            <div class="hero-visual">
                <div class="terminal-container">
                    <div class="terminal-header">
                        <div class="terminal-controls">
                            <span class="control close"></span>
                            <span class="control minimize"></span>
                            <span class="control maximize"></span>
                        </div>
                        <div class="terminal-title">grady@portfolio:~$</div>
                    </div>
                    <div class="terminal-body">
                        <div class="terminal-line">
                            <span class="prompt">grady@portfolio:~$</span>
                            <span class="command" id="terminal-command"></span>
                        </div>
                        <div class="terminal-output" id="terminal-output"></div>
                    </div>
                </div>

                <div class="floating-elements">
                    <div class="floating-icon" data-tech="react">⚛</div>
                    <div class="floating-icon" data-tech="js">JS</div>
                    <div class="floating-icon" data-tech="node">⬢</div>
                    <div class="floating-icon" data-tech="python">🐍</div>
                    <div class="floating-icon" data-tech="git">⚡</div>
                    <div class="floating-icon" data-tech="css">🎨</div>
                </div>
            </div>
        </div>

        <div class="scroll-indicator">
            <div class="scroll-text">Scroll to explore</div>
            <div class="scroll-arrow">
                <div class="arrow-line"></div>
                <div class="arrow-head"></div>
            </div>
        </div>

        <!-- Mouse follower -->
        <div class="mouse-follower" id="mouse-follower"></div>
    </section>

    <!-- About Section -->
    <section id="about" class="about">
        <div class="container">
            <h2 class="section-title">About Me</h2>
            <div class="about-content">
                <div class="about-text">
                    <p class="about-intro">
                        I'm a passionate software engineer with a strong foundation in both frontend and backend development. 
                        My journey in technology is driven by curiosity and a commitment to continuous learning.
                    </p>
                    <p>
                        With experience in modern web technologies and a keen eye for user experience, I enjoy building 
                        applications that are not only functional but also intuitive and engaging. I believe in writing 
                        clean, maintainable code and following best practices in software development.
                    </p>
                    <div class="about-highlights">
                        <div class="highlight-item">
                            <i class="fas fa-code"></i>
                            <span>Clean Code Advocate</span>
                        </div>
                        <div class="highlight-item">
                            <i class="fas fa-lightbulb"></i>
                            <span>Problem Solver</span>
                        </div>
                        <div class="highlight-item">
                            <i class="fas fa-users"></i>
                            <span>Team Collaborator</span>
                        </div>
                    </div>
                </div>
                <div class="about-image">
                    <div class="image-placeholder">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="skills">
        <div class="container">
            <h2 class="section-title">Technical Expertise</h2>

            <!-- Skills Navigation -->
            <div class="skills-nav">
                <button class="skill-nav-btn active" data-category="frontend">
                    <span class="nav-icon">🎨</span>
                    <span class="nav-text">Frontend</span>
                </button>
                <button class="skill-nav-btn" data-category="backend">
                    <span class="nav-icon">⚙️</span>
                    <span class="nav-text">Backend</span>
                </button>
                <button class="skill-nav-btn" data-category="tools">
                    <span class="nav-icon">🛠️</span>
                    <span class="nav-text">Tools</span>
                </button>
                <button class="skill-nav-btn" data-category="all">
                    <span class="nav-icon">🌟</span>
                    <span class="nav-text">All Skills</span>
                </button>
            </div>

            <!-- 3D Skills Visualization -->
            <div class="skills-3d-container">
                <div class="skills-scene" id="skills-scene">
                    <!-- 3D skills will be populated by JavaScript -->
                </div>

                <!-- Skill Details Panel -->
                <div class="skill-details-panel" id="skill-details">
                    <div class="skill-detail-content">
                        <h3 class="skill-detail-name">Select a skill</h3>
                        <div class="skill-detail-level">
                            <span class="level-label">Proficiency</span>
                            <div class="level-bar">
                                <div class="level-fill" style="width: 0%"></div>
                            </div>
                            <span class="level-text">Beginner</span>
                        </div>
                        <p class="skill-detail-description">
                            Hover over a skill sphere to see detailed information about my experience and proficiency level.
                        </p>
                        <div class="skill-projects">
                            <h4>Related Projects</h4>
                            <div class="project-tags">
                                <!-- Project tags will be populated -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Traditional Skills Grid (Fallback) -->
            <div class="skills-grid-fallback" style="display: none;">
                <div class="skills-category">
                    <h3 class="category-title">Frontend Development</h3>
                    <div class="skills-grid">
                        <!-- Skills will be populated by JavaScript -->
                    </div>
                </div>
                <div class="skills-category">
                    <h3 class="category-title">Backend Development</h3>
                    <div class="skills-grid">
                        <!-- Skills will be populated by JavaScript -->
                    </div>
                </div>
                <div class="skills-category">
                    <h3 class="category-title">Tools & Technologies</h3>
                    <div class="skills-grid">
                        <!-- Skills will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Skills Statistics -->
            <div class="skills-stats">
                <div class="stat-card">
                    <div class="stat-icon">💻</div>
                    <div class="stat-info">
                        <span class="stat-number" data-target="8">0</span>
                        <span class="stat-label">Frontend Technologies</span>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">⚡</div>
                    <div class="stat-info">
                        <span class="stat-number" data-target="8">0</span>
                        <span class="stat-label">Backend Technologies</span>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">🔧</div>
                    <div class="stat-info">
                        <span class="stat-number" data-target="8">0</span>
                        <span class="stat-label">Development Tools</span>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">🏆</div>
                    <div class="stat-info">
                        <span class="stat-number" data-target="5">0</span>
                        <span class="stat-label">Years Experience</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="projects">
        <div class="container">
            <h2 class="section-title">Featured Projects</h2>

            <!-- Advanced Project Filter -->
            <div class="projects-filter-advanced">
                <div class="filter-search">
                    <input type="text" id="project-search" placeholder="Search projects..." class="search-input">
                    <i class="fas fa-search search-icon"></i>
                </div>

                <div class="filter-buttons">
                    <button class="filter-btn-3d active" data-filter="all">
                        <span class="filter-icon">🌟</span>
                        <span class="filter-text">All Projects</span>
                        <span class="filter-count">6</span>
                    </button>
                    <button class="filter-btn-3d" data-filter="frontend">
                        <span class="filter-icon">🎨</span>
                        <span class="filter-text">Frontend</span>
                        <span class="filter-count">3</span>
                    </button>
                    <button class="filter-btn-3d" data-filter="backend">
                        <span class="filter-icon">⚙️</span>
                        <span class="filter-text">Backend</span>
                        <span class="filter-count">1</span>
                    </button>
                    <button class="filter-btn-3d" data-filter="fullstack">
                        <span class="filter-icon">🚀</span>
                        <span class="filter-text">Full Stack</span>
                        <span class="filter-count">2</span>
                    </button>
                </div>

                <div class="view-toggle">
                    <button class="view-btn active" data-view="grid">
                        <i class="fas fa-th"></i>
                    </button>
                    <button class="view-btn" data-view="list">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>

            <!-- Project Showcase -->
            <div class="projects-showcase">
                <!-- Featured Project Spotlight -->
                <div class="featured-project" id="featured-project">
                    <div class="featured-content">
                        <div class="featured-info">
                            <span class="featured-badge">Featured Project</span>
                            <h3 class="featured-title">E-Commerce Platform</h3>
                            <p class="featured-description">
                                A comprehensive full-stack e-commerce solution with advanced features
                                including real-time inventory, payment processing, and admin dashboard.
                            </p>
                            <div class="featured-tech">
                                <span class="tech-badge">React</span>
                                <span class="tech-badge">Node.js</span>
                                <span class="tech-badge">MongoDB</span>
                                <span class="tech-badge">Stripe</span>
                            </div>
                            <div class="featured-actions">
                                <a href="#" class="btn-featured primary">
                                    <i class="fab fa-github"></i>
                                    <span>View Code</span>
                                </a>
                                <a href="#" class="btn-featured secondary">
                                    <i class="fas fa-external-link-alt"></i>
                                    <span>Live Demo</span>
                                </a>
                            </div>
                        </div>
                        <div class="featured-visual">
                            <div class="project-mockup">
                                <div class="mockup-browser">
                                    <div class="browser-header">
                                        <div class="browser-controls">
                                            <span class="control red"></span>
                                            <span class="control yellow"></span>
                                            <span class="control green"></span>
                                        </div>
                                        <div class="browser-url">https://ecommerce-demo.com</div>
                                    </div>
                                    <div class="browser-content">
                                        <div class="content-placeholder">
                                            <div class="placeholder-header"></div>
                                            <div class="placeholder-grid">
                                                <div class="placeholder-item"></div>
                                                <div class="placeholder-item"></div>
                                                <div class="placeholder-item"></div>
                                                <div class="placeholder-item"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Projects Grid -->
                <div class="projects-grid-3d" id="projects-grid">
                    <!-- Projects will be populated by JavaScript -->
                </div>
            </div>

            <!-- Project Modal -->
            <div class="project-modal" id="project-modal">
                <div class="modal-overlay"></div>
                <div class="modal-content">
                    <button class="modal-close" id="modal-close">
                        <i class="fas fa-times"></i>
                    </button>
                    <div class="modal-body">
                        <div class="modal-header">
                            <h3 class="modal-title">Project Title</h3>
                            <div class="modal-tech">
                                <!-- Tech stack will be populated -->
                            </div>
                        </div>
                        <div class="modal-content-area">
                            <div class="modal-image">
                                <img src="" alt="Project Screenshot" class="modal-img">
                            </div>
                            <div class="modal-details">
                                <p class="modal-description">Project description...</p>
                                <div class="modal-features">
                                    <h4>Key Features</h4>
                                    <ul class="features-list">
                                        <!-- Features will be populated -->
                                    </ul>
                                </div>
                                <div class="modal-actions">
                                    <a href="#" class="btn-modal primary">
                                        <i class="fab fa-github"></i>
                                        <span>View Code</span>
                                    </a>
                                    <a href="#" class="btn-modal secondary">
                                        <i class="fas fa-external-link-alt"></i>
                                        <span>Live Demo</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="container">
            <h2 class="section-title">Let's Connect</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <p class="contact-description">
                        I'm always interested in new opportunities and collaborations. 
                        Feel free to reach out if you'd like to discuss a project or just say hello!
                    </p>
                    <div class="contact-methods">
                        <a href="mailto:<EMAIL>" class="contact-method">
                            <i class="fas fa-envelope"></i>
                            <span><EMAIL></span>
                        </a>
                        <a href="https://linkedin.com/in/yourprofile" class="contact-method" target="_blank">
                            <i class="fab fa-linkedin"></i>
                            <span>LinkedIn Profile</span>
                        </a>
                        <a href="https://github.com/yourusername" class="contact-method" target="_blank">
                            <i class="fab fa-github"></i>
                            <span>GitHub Profile</span>
                        </a>
                    </div>
                </div>
                <div class="contact-form">
                    <form id="contact-form">
                        <div class="form-group">
                            <input type="text" id="name" name="name" placeholder="Your Name" required>
                        </div>
                        <div class="form-group">
                            <input type="email" id="email" name="email" placeholder="Your Email" required>
                        </div>
                        <div class="form-group">
                            <textarea id="message" name="message" placeholder="Your Message" rows="5" required></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">Send Message</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p>&copy; 2025 Grady Rankin. All rights reserved.</p>
                <div class="footer-links">
                    <a href="#home">Home</a>
                    <a href="#about">About</a>
                    <a href="#projects">Projects</a>
                    <a href="#contact">Contact</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/data.js"></script>
    <script src="js/theme.js"></script>
    <script src="js/particles.js"></script>
    <script src="js/skills3d.js"></script>
    <script src="js/projects3d.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
