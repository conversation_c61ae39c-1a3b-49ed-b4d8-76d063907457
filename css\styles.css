/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
  /* Color Palette */
  --color-primary: #D4AF37;        /* Muted Golden Yellow */
  --color-primary-light: #E6C55A;  /* Lighter golden yellow */
  --color-primary-dark: #B8941F;   /* Darker golden yellow */
  
  --color-secondary: #6B7280;      /* Medium Grey */
  --color-secondary-light: #9CA3AF; /* Lighter grey */
  --color-secondary-dark: #4B5563; /* Darker grey */
  
  --color-dark: #1F2937;           /* Charcoal Black */
  --color-dark-light: #374151;     /* Lighter charcoal */
  --color-darker: #111827;         /* Darker charcoal */
  
  --color-white: #FFFFFF;
  --color-light: #F9FAFB;
  --color-border: #E5E7EB;
  
  /* Typography */
  --font-primary: 'Inter', sans-serif;
  --font-mono: 'JetBrains Mono', monospace;
  
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  
  /* Layout */
  --container-max-width: 1200px;
  --section-padding: 5rem 0;
  --border-radius: 0.5rem;
  --border-radius-lg: 1rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* ===== RESET & BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--color-dark);
  background-color: var(--color-white);
  overflow-x: hidden;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-5xl); }
h2 { font-size: var(--font-size-4xl); }
h3 { font-size: var(--font-size-3xl); }
h4 { font-size: var(--font-size-2xl); }
h5 { font-size: var(--font-size-xl); }
h6 { font-size: var(--font-size-lg); }

p {
  margin-bottom: var(--spacing-md);
  color: var(--color-secondary-dark);
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-primary-dark);
}

/* ===== UTILITY CLASSES ===== */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.section-title {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
  position: relative;
  color: var(--color-dark);
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
  border-radius: 2px;
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-block;
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-light));
  color: var(--color-white);
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  color: var(--color-white);
}

.btn-secondary {
  background: transparent;
  color: var(--color-dark);
  border: 2px solid var(--color-secondary);
}

.btn-secondary:hover {
  background: var(--color-secondary);
  color: var(--color-white);
  transform: translateY(-2px);
}

/* ===== NAVIGATION ===== */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--color-border);
  z-index: 1000;
  transition: all var(--transition-normal);
}

.navbar.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow-md);
}

.nav-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.nav-logo a {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--color-primary);
  text-decoration: none;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: var(--spacing-xl);
}

.nav-link {
  color: var(--color-dark);
  font-weight: 500;
  position: relative;
  transition: color var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
  color: var(--color-primary);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--color-primary);
  transition: width var(--transition-normal);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.bar {
  width: 25px;
  height: 3px;
  background: var(--color-dark);
  margin: 3px 0;
  transition: var(--transition-fast);
}

/* ===== HERO SECTION ===== */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--color-light) 0%, var(--color-white) 100%);
  position: relative;
  overflow: hidden;
}

.hero-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: center;
}

.hero-content {
  z-index: 2;
}

.hero-title {
  margin-bottom: var(--spacing-xl);
}

.hero-greeting {
  display: block;
  font-size: var(--font-size-lg);
  color: var(--color-secondary);
  font-weight: 400;
  margin-bottom: var(--spacing-sm);
}

.hero-name {
  display: block;
  font-size: var(--font-size-5xl);
  font-weight: 700;
  color: var(--color-dark);
  margin-bottom: var(--spacing-sm);
}

.hero-role {
  display: block;
  font-size: var(--font-size-2xl);
  color: var(--color-primary);
  font-weight: 500;
}

.hero-description {
  font-size: var(--font-size-lg);
  color: var(--color-secondary-dark);
  margin-bottom: var(--spacing-2xl);
  max-width: 500px;
}

.hero-buttons {
  display: flex;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.code-animation {
  width: 300px;
  height: 200px;
  background: var(--color-dark);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  position: relative;
  box-shadow: var(--shadow-xl);
}

.code-line {
  height: 20px;
  background: var(--color-secondary);
  margin-bottom: var(--spacing-md);
  border-radius: 2px;
  opacity: 0.7;
}

.code-line:nth-child(1) { width: 80%; }
.code-line:nth-child(2) { width: 60%; }
.code-line:nth-child(3) { width: 90%; }

.scroll-indicator {
  position: absolute;
  bottom: var(--spacing-xl);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--color-secondary);
}

.scroll-arrow {
  width: 20px;
  height: 20px;
  border-right: 2px solid var(--color-secondary);
  border-bottom: 2px solid var(--color-secondary);
  transform: rotate(45deg);
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0) rotate(45deg);
  }
  40% {
    transform: translateY(-10px) rotate(45deg);
  }
  60% {
    transform: translateY(-5px) rotate(45deg);
  }
}

/* ===== ABOUT SECTION ===== */
.about {
  padding: var(--section-padding);
  background: var(--color-white);
}

.about-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-3xl);
  align-items: center;
}

.about-text {
  max-width: 600px;
}

.about-intro {
  font-size: var(--font-size-lg);
  color: var(--color-dark);
  margin-bottom: var(--spacing-lg);
}

.about-highlights {
  display: flex;
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
  flex-wrap: wrap;
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--color-secondary-dark);
}

.highlight-item i {
  color: var(--color-primary);
  font-size: var(--font-size-lg);
}

.about-image {
  display: flex;
  justify-content: center;
}

.image-placeholder {
  width: 250px;
  height: 250px;
  background: linear-gradient(135deg, var(--color-secondary-light), var(--color-secondary));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-lg);
}

.image-placeholder i {
  font-size: 4rem;
  color: var(--color-white);
}

/* ===== SKILLS SECTION ===== */
.skills {
  padding: var(--section-padding);
  background: var(--color-light);
}

.skills-content {
  display: grid;
  gap: var(--spacing-3xl);
}

.skills-category {
  text-align: center;
}

.category-title {
  color: var(--color-dark);
  margin-bottom: var(--spacing-xl);
  font-size: var(--font-size-2xl);
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-lg);
  max-width: 800px;
  margin: 0 auto;
}

.skill-item {
  background: var(--color-white);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  cursor: pointer;
}

.skill-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.skill-icon {
  font-size: var(--font-size-3xl);
  color: var(--color-primary);
  margin-bottom: var(--spacing-sm);
}

.skill-name {
  font-weight: 500;
  color: var(--color-dark);
  margin-bottom: var(--spacing-xs);
}

.skill-level {
  font-size: var(--font-size-sm);
  color: var(--color-secondary);
}

/* ===== PROJECTS SECTION ===== */
.projects {
  padding: var(--section-padding);
  background: var(--color-white);
}

.projects-filter {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-3xl);
  flex-wrap: wrap;
}

.filter-btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  background: transparent;
  border: 2px solid var(--color-secondary-light);
  border-radius: var(--border-radius);
  color: var(--color-secondary-dark);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
  background: var(--color-primary);
  border-color: var(--color-primary);
  color: var(--color-white);
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
}

.project-card {
  background: var(--color-white);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  position: relative;
}

.project-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-xl);
}

.project-image {
  height: 200px;
  background: linear-gradient(135deg, var(--color-secondary-light), var(--color-secondary));
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.project-image i {
  font-size: 3rem;
  color: var(--color-white);
}

.project-content {
  padding: var(--spacing-lg);
}

.project-title {
  font-size: var(--font-size-xl);
  color: var(--color-dark);
  margin-bottom: var(--spacing-sm);
}

.project-description {
  color: var(--color-secondary-dark);
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.tech-tag {
  background: var(--color-light);
  color: var(--color-secondary-dark);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.project-links {
  display: flex;
  gap: var(--spacing-md);
}

.project-link {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--color-primary);
  font-weight: 500;
  transition: color var(--transition-fast);
}

.project-link:hover {
  color: var(--color-primary-dark);
}

/* ===== CONTACT SECTION ===== */
.contact {
  padding: var(--section-padding);
  background: var(--color-light);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: start;
}

.contact-description {
  font-size: var(--font-size-lg);
  color: var(--color-secondary-dark);
  margin-bottom: var(--spacing-xl);
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.contact-method {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  color: var(--color-dark);
  transition: color var(--transition-fast);
}

.contact-method:hover {
  color: var(--color-primary);
}

.contact-method i {
  font-size: var(--font-size-lg);
  width: 24px;
  text-align: center;
}

.contact-form {
  background: var(--color-white);
  padding: var(--spacing-2xl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-md);
  border: 2px solid var(--color-border);
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  font-family: var(--font-primary);
  transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--color-primary);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

/* ===== FOOTER ===== */
.footer {
  background: var(--color-dark);
  color: var(--color-white);
  padding: var(--spacing-xl) 0;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
}

.footer-links {
  display: flex;
  gap: var(--spacing-lg);
}

.footer-links a {
  color: var(--color-secondary-light);
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: var(--color-primary);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  /* Navigation */
  .nav-menu {
    position: fixed;
    left: -100%;
    top: 70px;
    flex-direction: column;
    background-color: var(--color-white);
    width: 100%;
    text-align: center;
    transition: 0.3s;
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-xl) 0;
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-menu li {
    margin: var(--spacing-md) 0;
  }

  .nav-toggle {
    display: flex;
  }

  .nav-toggle.active .bar:nth-child(2) {
    opacity: 0;
  }

  .nav-toggle.active .bar:nth-child(1) {
    transform: translateY(8px) rotate(45deg);
  }

  .nav-toggle.active .bar:nth-child(3) {
    transform: translateY(-8px) rotate(-45deg);
  }

  /* Hero Section */
  .hero-container {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--spacing-2xl);
  }

  .hero-name {
    font-size: var(--font-size-4xl);
  }

  .hero-role {
    font-size: var(--font-size-xl);
  }

  .hero-buttons {
    justify-content: center;
  }

  .code-animation {
    width: 250px;
    height: 150px;
  }

  /* About Section */
  .about-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--spacing-2xl);
  }

  .about-highlights {
    justify-content: center;
  }

  .image-placeholder {
    width: 200px;
    height: 200px;
  }

  /* Skills Section */
  .skills-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: var(--spacing-md);
  }

  /* Projects Section */
  .projects-grid {
    grid-template-columns: 1fr;
  }

  /* Contact Section */
  .contact-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }

  .contact-form {
    padding: var(--spacing-lg);
  }

  /* Footer */
  .footer-content {
    flex-direction: column;
    text-align: center;
  }

  /* Typography adjustments */
  h1 { font-size: var(--font-size-4xl); }
  h2 { font-size: var(--font-size-3xl); }
  h3 { font-size: var(--font-size-2xl); }

  .section-title {
    margin-bottom: var(--spacing-2xl);
  }

  /* Spacing adjustments */
  :root {
    --section-padding: 3rem 0;
  }

  .container {
    padding: 0 var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .hero-name {
    font-size: var(--font-size-3xl);
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 100%;
    max-width: 250px;
  }

  .projects-filter {
    flex-direction: column;
    align-items: center;
  }

  .filter-btn {
    width: 120px;
  }

  .about-highlights {
    flex-direction: column;
    align-items: center;
  }

  .skills-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* ===== UTILITY CLASSES ===== */
.hidden {
  display: none !important;
}

.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.text-center {
  text-align: center;
}

.text-primary {
  color: var(--color-primary);
}

.bg-primary {
  background-color: var(--color-primary);
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }
