/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
  /* Color Palette - Light Theme */
  --color-primary: #D4AF37;        /* Muted Golden Yellow */
  --color-primary-light: #E6C55A;  /* Lighter golden yellow */
  --color-primary-dark: #B8941F;   /* Darker golden yellow */

  --color-secondary: #6B7280;      /* Medium Grey */
  --color-secondary-light: #9CA3AF; /* Lighter grey */
  --color-secondary-dark: #4B5563; /* Darker grey */

  --color-dark: #1F2937;           /* Charcoal Black */
  --color-dark-light: #374151;     /* Lighter charcoal */
  --color-darker: #111827;         /* Darker charcoal */

  --color-white: #FFFFFF;
  --color-light: #F9FAFB;
  --color-border: #E5E7EB;

  /* Theme Variables */
  --bg-primary: var(--color-white);
  --bg-secondary: var(--color-light);
  --text-primary: var(--color-dark);
  --text-secondary: var(--color-secondary-dark);
  --border-color: var(--color-border);
}

/* Dark Theme */
[data-theme="dark"] {
  --color-white: #1F2937;
  --color-light: #111827;
  --color-border: #374151;

  --bg-primary: var(--color-darker);
  --bg-secondary: var(--color-dark);
  --text-primary: #F9FAFB;
  --text-secondary: var(--color-secondary-light);
  --border-color: var(--color-dark-light);
}
  
  /* Typography */
  --font-primary: 'Inter', sans-serif;
  --font-mono: 'JetBrains Mono', monospace;
  
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  
  /* Layout */
  --container-max-width: 1200px;
  --section-padding: 5rem 0;
  --border-radius: 0.5rem;
  --border-radius-lg: 1rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* ===== RESET & BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--color-dark);
  background-color: var(--color-white);
  overflow-x: hidden;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-5xl); }
h2 { font-size: var(--font-size-4xl); }
h3 { font-size: var(--font-size-3xl); }
h4 { font-size: var(--font-size-2xl); }
h5 { font-size: var(--font-size-xl); }
h6 { font-size: var(--font-size-lg); }

p {
  margin-bottom: var(--spacing-md);
  color: var(--color-secondary-dark);
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-primary-dark);
}

/* ===== UTILITY CLASSES ===== */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.section-title {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
  position: relative;
  color: var(--color-dark);
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
  border-radius: 2px;
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-block;
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-light));
  color: var(--color-white);
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  color: var(--color-white);
}

.btn-secondary {
  background: transparent;
  color: var(--color-dark);
  border: 2px solid var(--color-secondary);
}

.btn-secondary:hover {
  background: var(--color-secondary);
  color: var(--color-white);
  transform: translateY(-2px);
}

/* ===== NAVIGATION ===== */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--color-border);
  z-index: 1000;
  transition: all var(--transition-normal);
}

.navbar.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow-md);
}

.nav-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.nav-logo a {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--color-primary);
  text-decoration: none;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: var(--spacing-xl);
}

.nav-link {
  color: var(--color-dark);
  font-weight: 500;
  position: relative;
  transition: color var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
  color: var(--color-primary);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--color-primary);
  transition: width var(--transition-normal);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

/* Theme Toggle */
.theme-toggle {
  background: none;
  border: 2px solid var(--color-primary);
  border-radius: 50px;
  padding: var(--spacing-sm);
  cursor: pointer;
  position: relative;
  width: 60px;
  height: 30px;
  transition: all var(--transition-normal);
  overflow: hidden;
}

.theme-toggle:hover {
  box-shadow: 0 0 20px rgba(212, 175, 55, 0.3);
}

.theme-icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  transition: all var(--transition-normal);
}

.theme-icon.sun {
  left: 5px;
  opacity: 1;
}

.theme-icon.moon {
  right: 5px;
  opacity: 0;
}

[data-theme="dark"] .theme-icon.sun {
  opacity: 0;
  left: -20px;
}

[data-theme="dark"] .theme-icon.moon {
  opacity: 1;
  right: 5px;
}

.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.bar {
  width: 25px;
  height: 3px;
  background: var(--text-primary);
  margin: 3px 0;
  transition: var(--transition-fast);
}

/* ===== HERO SECTION ===== */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: radial-gradient(ellipse at center, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
              linear-gradient(135deg, var(--color-darker) 0%, var(--color-dark) 50%, var(--color-dark-light) 100%);
  position: relative;
  overflow: hidden;
}

/* Particle Canvas */
.particle-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

/* Geometric Background */
.geometric-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.geometric-shape {
  position: absolute;
  border: 1px solid rgba(212, 175, 55, 0.2);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 100px;
  height: 100px;
  top: 20%;
  left: 10%;
  transform: rotate(45deg);
  animation-delay: 0s;
}

.shape-2 {
  width: 60px;
  height: 60px;
  top: 60%;
  right: 15%;
  border-radius: 50%;
  animation-delay: 2s;
}

.shape-3 {
  width: 80px;
  height: 80px;
  bottom: 30%;
  left: 20%;
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  animation-delay: 4s;
}

.shape-4 {
  width: 120px;
  height: 120px;
  top: 10%;
  right: 30%;
  transform: rotate(30deg);
  animation-delay: 1s;
}

.hero-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: center;
  z-index: 2;
  position: relative;
}

.hero-content {
  z-index: 3;
}

/* Hero Intro */
.hero-intro {
  margin-bottom: var(--spacing-lg);
}

.hero-greeting {
  font-size: var(--font-size-lg);
  color: var(--color-primary);
  font-weight: 400;
  position: relative;
  display: inline-block;
}

/* Glitch Effect */
.glitch {
  position: relative;
  animation: glitch 2s infinite;
}

.glitch::before,
.glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch::before {
  animation: glitch-1 0.5s infinite;
  color: #ff0000;
  z-index: -1;
}

.glitch::after {
  animation: glitch-2 0.5s infinite;
  color: #00ff00;
  z-index: -2;
}

/* Hero Title */
.hero-title {
  margin-bottom: var(--spacing-xl);
}

.hero-name {
  display: block;
  font-size: clamp(2.5rem, 8vw, 4.5rem);
  font-weight: 700;
  color: var(--color-white);
  margin-bottom: var(--spacing-lg);
  line-height: 1.1;
}

.letter {
  display: inline-block;
  animation: letterFloat 3s ease-in-out infinite;
  animation-fill-mode: both;
}

.letter:nth-child(1) { animation-delay: 0.1s; }
.letter:nth-child(2) { animation-delay: 0.2s; }
.letter:nth-child(3) { animation-delay: 0.3s; }
.letter:nth-child(4) { animation-delay: 0.4s; }
.letter:nth-child(5) { animation-delay: 0.5s; }
.letter:nth-child(7) { animation-delay: 0.7s; }
.letter:nth-child(8) { animation-delay: 0.8s; }
.letter:nth-child(9) { animation-delay: 0.9s; }
.letter:nth-child(10) { animation-delay: 1.0s; }
.letter:nth-child(11) { animation-delay: 1.1s; }
.letter:nth-child(12) { animation-delay: 1.2s; }

/* Hero Role */
.hero-role-container {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  font-size: var(--font-size-2xl);
  color: var(--color-white);
}

.hero-role-prefix {
  color: var(--color-secondary-light);
  margin-right: var(--spacing-sm);
}

.typewriter {
  color: var(--color-primary);
  font-weight: 600;
  min-width: 200px;
}

.cursor {
  color: var(--color-primary);
  animation: blink 1s infinite;
  font-weight: 100;
}

/* Hero Description */
.hero-description {
  margin-bottom: var(--spacing-2xl);
  max-width: 500px;
}

.description-line {
  display: block;
  font-size: var(--font-size-lg);
  color: var(--color-secondary-light);
  margin-bottom: var(--spacing-sm);
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s ease-out forwards;
}

.description-line:nth-child(1) { animation-delay: 1s; }
.description-line:nth-child(2) { animation-delay: 1.2s; }
.description-line:nth-child(3) { animation-delay: 1.4s; }

/* Hero Stats */
.hero-stats {
  display: flex;
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
  flex-wrap: wrap;
}

.stat-item {
  text-align: center;
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease-out forwards;
}

.stat-item:nth-child(1) { animation-delay: 1.6s; }
.stat-item:nth-child(2) { animation-delay: 1.8s; }
.stat-item:nth-child(3) { animation-delay: 2s; }

.stat-number {
  display: block;
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--color-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-secondary-light);
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Enhanced Buttons */
.hero-buttons {
  display: flex;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.btn-3d {
  position: relative;
  transform-style: preserve-3d;
  transition: all 0.3s ease;
  overflow: hidden;
}

.btn-3d::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.btn-3d:hover::before {
  transform: translateX(100%);
}

.btn-3d:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 10px 25px rgba(212, 175, 55, 0.3);
}

.btn-text {
  margin-right: var(--spacing-sm);
}

.btn-icon {
  transition: transform 0.3s ease;
}

.btn-3d:hover .btn-icon {
  transform: translateX(5px);
}

/* Terminal */
.terminal-container {
  width: 400px;
  height: 300px;
  background: var(--color-darker);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  position: relative;
  transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
  transition: transform 0.3s ease;
}

.terminal-container:hover {
  transform: perspective(1000px) rotateY(0deg) rotateX(0deg) scale(1.02);
}

.terminal-header {
  background: var(--color-dark-light);
  padding: var(--spacing-sm) var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--color-secondary-dark);
}

.terminal-controls {
  display: flex;
  gap: var(--spacing-sm);
}

.control {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.control.close { background: #ff5f57; }
.control.minimize { background: #ffbd2e; }
.control.maximize { background: #28ca42; }

.terminal-title {
  color: var(--color-secondary-light);
  font-family: var(--font-mono);
  font-size: var(--font-size-sm);
}

.terminal-body {
  padding: var(--spacing-md);
  font-family: var(--font-mono);
  font-size: var(--font-size-sm);
  color: var(--color-white);
  height: calc(100% - 40px);
  overflow-y: auto;
}

.terminal-line {
  margin-bottom: var(--spacing-sm);
}

.prompt {
  color: var(--color-primary);
  margin-right: var(--spacing-sm);
}

.command {
  color: var(--color-white);
}

.terminal-output {
  color: var(--color-secondary-light);
  margin-left: var(--spacing-lg);
}

/* Floating Elements */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-icon {
  position: absolute;
  width: 40px;
  height: 40px;
  background: rgba(212, 175, 55, 0.1);
  border: 1px solid var(--color-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  color: var(--color-primary);
  animation: floatTech 4s ease-in-out infinite;
}

.floating-icon[data-tech="react"] { top: 20%; left: 80%; animation-delay: 0s; }
.floating-icon[data-tech="js"] { top: 40%; left: 85%; animation-delay: 1s; }
.floating-icon[data-tech="node"] { top: 60%; left: 75%; animation-delay: 2s; }
.floating-icon[data-tech="python"] { top: 80%; left: 80%; animation-delay: 3s; }
.floating-icon[data-tech="git"] { top: 30%; left: 90%; animation-delay: 0.5s; }
.floating-icon[data-tech="css"] { top: 70%; left: 90%; animation-delay: 1.5s; }

/* Enhanced Scroll Indicator */
.scroll-indicator {
  position: absolute;
  bottom: var(--spacing-xl);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--color-secondary-light);
  z-index: 3;
}

.scroll-text {
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-sm);
  opacity: 0.8;
}

.scroll-arrow {
  position: relative;
  animation: scrollBounce 2s infinite;
}

.arrow-line {
  width: 2px;
  height: 30px;
  background: var(--color-primary);
  margin: 0 auto;
}

.arrow-head {
  width: 10px;
  height: 10px;
  border-right: 2px solid var(--color-primary);
  border-bottom: 2px solid var(--color-primary);
  transform: rotate(45deg);
  margin: -5px auto 0;
}

/* Mouse Follower */
.mouse-follower {
  position: fixed;
  width: 20px;
  height: 20px;
  background: radial-gradient(circle, var(--color-primary) 0%, transparent 70%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  transition: transform 0.1s ease;
  opacity: 0.6;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0) rotate(45deg);
  }
  40% {
    transform: translateY(-10px) rotate(45deg);
  }
  60% {
    transform: translateY(-5px) rotate(45deg);
  }
}

/* ===== ABOUT SECTION ===== */
.about {
  padding: var(--section-padding);
  background: var(--color-white);
}

.about-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-3xl);
  align-items: center;
}

.about-text {
  max-width: 600px;
}

.about-intro {
  font-size: var(--font-size-lg);
  color: var(--color-dark);
  margin-bottom: var(--spacing-lg);
}

.about-highlights {
  display: flex;
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
  flex-wrap: wrap;
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--color-secondary-dark);
}

.highlight-item i {
  color: var(--color-primary);
  font-size: var(--font-size-lg);
}

.about-image {
  display: flex;
  justify-content: center;
}

.image-placeholder {
  width: 250px;
  height: 250px;
  background: linear-gradient(135deg, var(--color-secondary-light), var(--color-secondary));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-lg);
}

.image-placeholder i {
  font-size: 4rem;
  color: var(--color-white);
}

/* ===== ADVANCED SKILLS SECTION ===== */
.skills {
  padding: var(--section-padding);
  background: linear-gradient(135deg, var(--color-darker) 0%, var(--color-dark) 100%);
  color: var(--color-white);
  position: relative;
  overflow: hidden;
}

.skills::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 30% 70%, rgba(212, 175, 55, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

/* Skills Navigation */
.skills-nav {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-3xl);
  flex-wrap: wrap;
}

.skill-nav-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(212, 175, 55, 0.3);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--color-white);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  backdrop-filter: blur(10px);
}

.skill-nav-btn:hover,
.skill-nav-btn.active {
  background: rgba(212, 175, 55, 0.2);
  border-color: var(--color-primary);
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(212, 175, 55, 0.2);
}

.nav-icon {
  font-size: var(--font-size-lg);
}

.nav-text {
  font-weight: 500;
}

/* 3D Skills Container */
.skills-3d-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-3xl);
  margin-bottom: var(--spacing-3xl);
  min-height: 500px;
}

.skills-scene {
  position: relative;
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--border-radius-lg);
  border: 1px solid rgba(212, 175, 55, 0.2);
  overflow: hidden;
  perspective: 1000px;
}

/* 3D Skill Spheres */
.skill-sphere {
  position: absolute;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-light));
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  transform-style: preserve-3d;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  animation: float 6s ease-in-out infinite;
}

.skill-sphere:hover {
  transform: scale(1.2) rotateY(180deg);
  box-shadow: 0 20px 40px rgba(212, 175, 55, 0.4);
  z-index: 10;
}

.skill-sphere .skill-icon {
  font-size: var(--font-size-2xl);
  color: var(--color-white);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.skill-sphere .skill-name {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  font-size: var(--font-size-sm);
  color: var(--color-white);
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.skill-sphere:hover .skill-name {
  opacity: 1;
}

/* Skill Level Indicator */
.skill-level-ring {
  position: absolute;
  top: -5px;
  left: -5px;
  width: 90px;
  height: 90px;
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  transform: rotate(-90deg);
  transition: all 0.3s ease;
}

.skill-sphere:hover .skill-level-ring {
  border-top-color: var(--color-white);
  animation: spin 2s linear infinite;
}

/* Skill Details Panel */
.skill-details-panel {
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
  border: 1px solid rgba(212, 175, 55, 0.2);
  padding: var(--spacing-xl);
  backdrop-filter: blur(10px);
  height: fit-content;
}

.skill-detail-content {
  text-align: left;
}

.skill-detail-name {
  color: var(--color-primary);
  margin-bottom: var(--spacing-lg);
  font-size: var(--font-size-2xl);
}

.skill-detail-level {
  margin-bottom: var(--spacing-lg);
}

.level-label {
  display: block;
  color: var(--color-secondary-light);
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-sm);
}

.level-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: var(--spacing-sm);
}

.level-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
  border-radius: 4px;
  transition: width 1s ease;
}

.level-text {
  color: var(--color-primary);
  font-weight: 600;
  font-size: var(--font-size-sm);
}

.skill-detail-description {
  color: var(--color-secondary-light);
  line-height: 1.6;
  margin-bottom: var(--spacing-lg);
}

.skill-projects h4 {
  color: var(--color-white);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-lg);
}

.project-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.project-tag {
  background: rgba(212, 175, 55, 0.2);
  color: var(--color-primary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-size: var(--font-size-xs);
  border: 1px solid rgba(212, 175, 55, 0.3);
}

/* Skills Statistics */
.skills-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-3xl);
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
  border: 1px solid rgba(212, 175, 55, 0.2);
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(212, 175, 55, 0.2);
}

.stat-icon {
  font-size: var(--font-size-3xl);
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(212, 175, 55, 0.2);
  border-radius: 50%;
}

.stat-info {
  flex: 1;
}

.stat-card .stat-number {
  display: block;
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--color-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-card .stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-secondary-light);
}

/* ===== ADVANCED PROJECTS SECTION ===== */
.projects {
  padding: var(--section-padding);
  background: linear-gradient(135deg, var(--color-light) 0%, var(--color-white) 100%);
  position: relative;
  overflow: hidden;
}

.projects::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 50%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(212, 175, 55, 0.05) 0%, transparent 70%);
  pointer-events: none;
}

/* Advanced Project Filter */
.projects-filter-advanced {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: var(--spacing-xl);
  align-items: center;
  margin-bottom: var(--spacing-3xl);
  padding: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.8);
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(212, 175, 55, 0.2);
}

.filter-search {
  position: relative;
  max-width: 300px;
}

.search-input {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-md) var(--spacing-md) 3rem;
  border: 2px solid var(--color-border);
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-base);
  transition: all var(--transition-normal);
  background: var(--color-white);
}

.search-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.search-icon {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-secondary);
}

.filter-buttons {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.filter-btn-3d {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--color-white);
  border: 2px solid var(--color-border);
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  transform-style: preserve-3d;
}

.filter-btn-3d::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.2), transparent);
  transition: left 0.5s ease;
}

.filter-btn-3d:hover::before {
  left: 100%;
}

.filter-btn-3d:hover,
.filter-btn-3d.active {
  background: var(--color-primary);
  border-color: var(--color-primary);
  color: var(--color-white);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
}

.filter-icon {
  font-size: var(--font-size-lg);
}

.filter-text {
  font-weight: 500;
}

.filter-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: var(--font-size-xs);
  font-weight: 600;
}

.view-toggle {
  display: flex;
  background: var(--color-white);
  border-radius: var(--border-radius);
  overflow: hidden;
  border: 2px solid var(--color-border);
}

.view-btn {
  padding: var(--spacing-md);
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--color-secondary-dark);
}

.view-btn:hover,
.view-btn.active {
  background: var(--color-primary);
  color: var(--color-white);
}

/* Featured Project Spotlight */
.featured-project {
  margin-bottom: var(--spacing-3xl);
  background: linear-gradient(135deg, var(--color-dark) 0%, var(--color-dark-light) 100%);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  position: relative;
  box-shadow: var(--shadow-xl);
}

.featured-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  min-height: 400px;
}

.featured-info {
  padding: var(--spacing-3xl);
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: var(--color-white);
}

.featured-badge {
  display: inline-block;
  background: var(--color-primary);
  color: var(--color-white);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  font-weight: 600;
  margin-bottom: var(--spacing-lg);
  width: fit-content;
}

.featured-title {
  font-size: var(--font-size-4xl);
  color: var(--color-white);
  margin-bottom: var(--spacing-lg);
}

.featured-description {
  font-size: var(--font-size-lg);
  color: var(--color-secondary-light);
  margin-bottom: var(--spacing-xl);
  line-height: 1.6;
}

.featured-tech {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xl);
}

.tech-badge {
  background: rgba(212, 175, 55, 0.2);
  color: var(--color-primary);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  font-weight: 500;
  border: 1px solid rgba(212, 175, 55, 0.3);
}

.featured-actions {
  display: flex;
  gap: var(--spacing-lg);
}

.btn-featured {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  text-decoration: none;
  font-weight: 600;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.btn-featured.primary {
  background: var(--color-primary);
  color: var(--color-white);
}

.btn-featured.secondary {
  background: transparent;
  color: var(--color-white);
  border: 2px solid var(--color-white);
}

.btn-featured:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Featured Visual */
.featured-visual {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  background: linear-gradient(45deg, rgba(212, 175, 55, 0.1), transparent);
}

.project-mockup {
  width: 100%;
  max-width: 400px;
  transform: perspective(1000px) rotateY(-10deg) rotateX(5deg);
  transition: transform 0.3s ease;
}

.project-mockup:hover {
  transform: perspective(1000px) rotateY(0deg) rotateX(0deg) scale(1.05);
}

.mockup-browser {
  background: var(--color-white);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.browser-header {
  background: var(--color-light);
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
}

.browser-controls {
  display: flex;
  gap: var(--spacing-xs);
}

.control {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.control.red { background: #ff5f57; }
.control.yellow { background: #ffbd2e; }
.control.green { background: #28ca42; }

.browser-url {
  flex: 1;
  background: var(--color-white);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  color: var(--color-secondary-dark);
}

.browser-content {
  height: 250px;
  background: var(--color-white);
  padding: var(--spacing-lg);
}

.content-placeholder {
  height: 100%;
}

.placeholder-header {
  height: 40px;
  background: linear-gradient(90deg, var(--color-light), var(--color-border));
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-lg);
  animation: shimmer 2s infinite;
}

.placeholder-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
  height: calc(100% - 60px);
}

.placeholder-item {
  background: linear-gradient(90deg, var(--color-light), var(--color-border));
  border-radius: var(--border-radius);
  animation: shimmer 2s infinite;
}

.placeholder-item:nth-child(2) { animation-delay: 0.2s; }
.placeholder-item:nth-child(3) { animation-delay: 0.4s; }
.placeholder-item:nth-child(4) { animation-delay: 0.6s; }

/* 3D Projects Grid */
.projects-grid-3d {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
  perspective: 1000px;
}

.project-card-3d {
  background: var(--color-white);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  position: relative;
  transform-style: preserve-3d;
  cursor: pointer;
}

.project-card-3d::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent 30%, rgba(212, 175, 55, 0.1) 50%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 1;
}

.project-card-3d:hover::before {
  opacity: 1;
}

.project-card-3d:hover {
  transform: translateY(-15px) rotateX(5deg) rotateY(5deg);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

.project-image-3d {
  height: 220px;
  background: linear-gradient(135deg, var(--color-secondary-light), var(--color-secondary));
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.project-image-3d::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.project-card-3d:hover .project-image-3d::after {
  transform: translateX(100%);
}

.project-image-3d i {
  font-size: 4rem;
  color: var(--color-white);
  z-index: 2;
  position: relative;
}

.project-content-3d {
  padding: var(--spacing-xl);
  position: relative;
  z-index: 2;
}

.project-title-3d {
  font-size: var(--font-size-xl);
  color: var(--color-dark);
  margin-bottom: var(--spacing-md);
  font-weight: 600;
}

.project-description-3d {
  color: var(--color-secondary-dark);
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.project-tech-3d {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.tech-tag-3d {
  background: var(--color-light);
  color: var(--color-secondary-dark);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  font-weight: 500;
  transition: all var(--transition-fast);
}

.project-card-3d:hover .tech-tag-3d {
  background: rgba(212, 175, 55, 0.1);
  color: var(--color-primary);
}

.project-links-3d {
  display: flex;
  gap: var(--spacing-md);
}

.project-link-3d {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--color-primary);
  font-weight: 500;
  transition: all var(--transition-fast);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  background: rgba(212, 175, 55, 0.1);
}

.project-link-3d:hover {
  background: var(--color-primary);
  color: var(--color-white);
  transform: translateY(-2px);
}

/* Project Modal */
.project-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: none;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
}

.project-modal.active {
  display: flex;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  animation: fadeIn 0.3s ease;
}

.modal-content {
  background: var(--color-white);
  border-radius: var(--border-radius-lg);
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  z-index: 1001;
  animation: modalSlideIn 0.3s ease;
}

.modal-close {
  position: absolute;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  width: 40px;
  height: 40px;
  background: var(--color-dark);
  color: var(--color-white);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1002;
  transition: all var(--transition-fast);
}

.modal-close:hover {
  background: var(--color-primary);
  transform: scale(1.1);
}

.modal-body {
  padding: var(--spacing-3xl);
}

.modal-header {
  margin-bottom: var(--spacing-xl);
}

.modal-title {
  font-size: var(--font-size-3xl);
  color: var(--color-dark);
  margin-bottom: var(--spacing-md);
}

.modal-tech {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.modal-content-area {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: start;
}

.modal-image {
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.modal-img {
  width: 100%;
  height: auto;
  display: block;
}

.modal-details {
  padding: var(--spacing-lg);
}

.modal-description {
  color: var(--color-secondary-dark);
  line-height: 1.6;
  margin-bottom: var(--spacing-xl);
  font-size: var(--font-size-lg);
}

.modal-features h4 {
  color: var(--color-dark);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-xl);
}

.features-list {
  list-style: none;
  margin-bottom: var(--spacing-xl);
}

.features-list li {
  color: var(--color-secondary-dark);
  margin-bottom: var(--spacing-sm);
  position: relative;
  padding-left: var(--spacing-lg);
}

.features-list li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--color-primary);
  font-weight: bold;
}

.modal-actions {
  display: flex;
  gap: var(--spacing-lg);
}

.btn-modal {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  text-decoration: none;
  font-weight: 600;
  transition: all var(--transition-normal);
}

.btn-modal.primary {
  background: var(--color-primary);
  color: var(--color-white);
}

.btn-modal.secondary {
  background: transparent;
  color: var(--color-primary);
  border: 2px solid var(--color-primary);
}

.btn-modal:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(212, 175, 55, 0.3);
}

/* ===== CONTACT SECTION ===== */
.contact {
  padding: var(--section-padding);
  background: var(--color-light);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: start;
}

.contact-description {
  font-size: var(--font-size-lg);
  color: var(--color-secondary-dark);
  margin-bottom: var(--spacing-xl);
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.contact-method {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  color: var(--color-dark);
  transition: color var(--transition-fast);
}

.contact-method:hover {
  color: var(--color-primary);
}

.contact-method i {
  font-size: var(--font-size-lg);
  width: 24px;
  text-align: center;
}

.contact-form {
  background: var(--color-white);
  padding: var(--spacing-2xl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-md);
  border: 2px solid var(--color-border);
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  font-family: var(--font-primary);
  transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--color-primary);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

/* ===== FOOTER ===== */
.footer {
  background: var(--color-dark);
  color: var(--color-white);
  padding: var(--spacing-xl) 0;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
}

.footer-links {
  display: flex;
  gap: var(--spacing-lg);
}

.footer-links a {
  color: var(--color-secondary-light);
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: var(--color-primary);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  /* Navigation */
  .nav-menu {
    position: fixed;
    left: -100%;
    top: 70px;
    flex-direction: column;
    background-color: var(--color-white);
    width: 100%;
    text-align: center;
    transition: 0.3s;
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-xl) 0;
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-menu li {
    margin: var(--spacing-md) 0;
  }

  .nav-toggle {
    display: flex;
  }

  .nav-toggle.active .bar:nth-child(2) {
    opacity: 0;
  }

  .nav-toggle.active .bar:nth-child(1) {
    transform: translateY(8px) rotate(45deg);
  }

  .nav-toggle.active .bar:nth-child(3) {
    transform: translateY(-8px) rotate(-45deg);
  }

  /* Hero Section */
  .hero-container {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--spacing-2xl);
  }

  .hero-name {
    font-size: var(--font-size-4xl);
  }

  .hero-role {
    font-size: var(--font-size-xl);
  }

  .hero-buttons {
    justify-content: center;
  }

  .code-animation {
    width: 250px;
    height: 150px;
  }

  /* About Section */
  .about-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--spacing-2xl);
  }

  .about-highlights {
    justify-content: center;
  }

  .image-placeholder {
    width: 200px;
    height: 200px;
  }

  /* Skills Section */
  .skills-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: var(--spacing-md);
  }

  /* Projects Section */
  .projects-grid {
    grid-template-columns: 1fr;
  }

  /* Contact Section */
  .contact-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }

  .contact-form {
    padding: var(--spacing-lg);
  }

  /* Footer */
  .footer-content {
    flex-direction: column;
    text-align: center;
  }

  /* Typography adjustments */
  h1 { font-size: var(--font-size-4xl); }
  h2 { font-size: var(--font-size-3xl); }
  h3 { font-size: var(--font-size-2xl); }

  .section-title {
    margin-bottom: var(--spacing-2xl);
  }

  /* Spacing adjustments */
  :root {
    --section-padding: 3rem 0;
  }

  .container {
    padding: 0 var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .hero-name {
    font-size: var(--font-size-3xl);
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 100%;
    max-width: 250px;
  }

  .projects-filter {
    flex-direction: column;
    align-items: center;
  }

  .filter-btn {
    width: 120px;
  }

  .about-highlights {
    flex-direction: column;
    align-items: center;
  }

  .skills-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* ===== UTILITY CLASSES ===== */
.hidden {
  display: none !important;
}

.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.text-center {
  text-align: center;
}

.text-primary {
  color: var(--color-primary);
}

.bg-primary {
  background-color: var(--color-primary);
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }
