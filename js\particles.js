// ===== ADVANCED PARTICLE SYSTEM =====

class ParticleSystem {
  constructor(canvas) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d');
    this.particles = [];
    this.mouse = { x: 0, y: 0 };
    this.connections = [];
    
    this.init();
    this.setupEventListeners();
    this.animate();
  }

  init() {
    this.resizeCanvas();
    this.createParticles();
  }

  resizeCanvas() {
    this.canvas.width = window.innerWidth;
    this.canvas.height = window.innerHeight;
  }

  createParticles() {
    const particleCount = Math.floor((this.canvas.width * this.canvas.height) / 15000);
    
    for (let i = 0; i < particleCount; i++) {
      this.particles.push({
        x: Math.random() * this.canvas.width,
        y: Math.random() * this.canvas.height,
        vx: (Math.random() - 0.5) * 0.5,
        vy: (Math.random() - 0.5) * 0.5,
        size: Math.random() * 2 + 1,
        opacity: Math.random() * 0.5 + 0.2,
        color: this.getRandomColor()
      });
    }
  }

  getRandomColor() {
    const colors = [
      'rgba(212, 175, 55, 0.8)',  // Primary gold
      'rgba(107, 114, 128, 0.6)', // Secondary grey
      'rgba(255, 255, 255, 0.4)'  // White
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  setupEventListeners() {
    window.addEventListener('resize', () => {
      this.resizeCanvas();
      this.particles = [];
      this.createParticles();
    });

    window.addEventListener('mousemove', (e) => {
      this.mouse.x = e.clientX;
      this.mouse.y = e.clientY;
      
      // Update mouse follower
      const follower = document.getElementById('mouse-follower');
      if (follower) {
        follower.style.left = e.clientX - 10 + 'px';
        follower.style.top = e.clientY - 10 + 'px';
      }
    });
  }

  updateParticles() {
    this.particles.forEach(particle => {
      // Update position
      particle.x += particle.vx;
      particle.y += particle.vy;

      // Mouse interaction
      const dx = this.mouse.x - particle.x;
      const dy = this.mouse.y - particle.y;
      const distance = Math.sqrt(dx * dx + dy * dy);

      if (distance < 100) {
        const force = (100 - distance) / 100;
        particle.vx += (dx / distance) * force * 0.01;
        particle.vy += (dy / distance) * force * 0.01;
      }

      // Boundary collision
      if (particle.x < 0 || particle.x > this.canvas.width) {
        particle.vx *= -1;
      }
      if (particle.y < 0 || particle.y > this.canvas.height) {
        particle.vy *= -1;
      }

      // Keep particles in bounds
      particle.x = Math.max(0, Math.min(this.canvas.width, particle.x));
      particle.y = Math.max(0, Math.min(this.canvas.height, particle.y));

      // Add some randomness
      particle.vx += (Math.random() - 0.5) * 0.01;
      particle.vy += (Math.random() - 0.5) * 0.01;

      // Limit velocity
      const maxVelocity = 2;
      particle.vx = Math.max(-maxVelocity, Math.min(maxVelocity, particle.vx));
      particle.vy = Math.max(-maxVelocity, Math.min(maxVelocity, particle.vy));
    });
  }

  drawConnections() {
    this.connections = [];
    
    for (let i = 0; i < this.particles.length; i++) {
      for (let j = i + 1; j < this.particles.length; j++) {
        const dx = this.particles[i].x - this.particles[j].x;
        const dy = this.particles[i].y - this.particles[j].y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < 120) {
          const opacity = (120 - distance) / 120 * 0.3;
          
          this.ctx.beginPath();
          this.ctx.strokeStyle = `rgba(212, 175, 55, ${opacity})`;
          this.ctx.lineWidth = 1;
          this.ctx.moveTo(this.particles[i].x, this.particles[i].y);
          this.ctx.lineTo(this.particles[j].x, this.particles[j].y);
          this.ctx.stroke();
        }
      }
    }
  }

  drawParticles() {
    this.particles.forEach(particle => {
      this.ctx.beginPath();
      this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
      this.ctx.fillStyle = particle.color;
      this.ctx.fill();

      // Add glow effect
      this.ctx.shadowBlur = 10;
      this.ctx.shadowColor = particle.color;
      this.ctx.fill();
      this.ctx.shadowBlur = 0;
    });
  }

  animate() {
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    
    this.updateParticles();
    this.drawConnections();
    this.drawParticles();
    
    requestAnimationFrame(() => this.animate());
  }
}

// ===== INTERACTIVE TERMINAL =====

class InteractiveTerminal {
  constructor() {
    this.commands = [
      'npm install creativity',
      'git commit -m "Added awesome features"',
      'python solve_problems.py',
      'node build_amazing_apps.js',
      'docker run innovation',
      'yarn start development'
    ];
    
    this.outputs = [
      '✓ Creativity installed successfully',
      '✓ Features committed to main branch',
      '✓ Problems solved efficiently',
      '✓ Amazing apps built and deployed',
      '✓ Innovation container running',
      '✓ Development server started on port 3000'
    ];
    
    this.currentCommandIndex = 0;
    this.commandElement = document.getElementById('terminal-command');
    this.outputElement = document.getElementById('terminal-output');
    
    this.startTerminalAnimation();
  }

  async startTerminalAnimation() {
    while (true) {
      await this.typeCommand();
      await this.showOutput();
      await this.wait(2000);
      this.clearTerminal();
      await this.wait(500);
      this.nextCommand();
    }
  }

  async typeCommand() {
    const command = this.commands[this.currentCommandIndex];
    this.commandElement.textContent = '';
    
    for (let i = 0; i < command.length; i++) {
      this.commandElement.textContent += command[i];
      await this.wait(50 + Math.random() * 50);
    }
  }

  async showOutput() {
    await this.wait(500);
    const output = this.outputs[this.currentCommandIndex];
    this.outputElement.textContent = output;
    this.outputElement.style.color = '#10B981'; // Success green
  }

  clearTerminal() {
    this.commandElement.textContent = '';
    this.outputElement.textContent = '';
  }

  nextCommand() {
    this.currentCommandIndex = (this.currentCommandIndex + 1) % this.commands.length;
  }

  wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// ===== TYPEWRITER EFFECT =====

class TypewriterEffect {
  constructor(element, texts, speed = 100) {
    this.element = element;
    this.texts = texts;
    this.speed = speed;
    this.textIndex = 0;
    this.charIndex = 0;
    this.isDeleting = false;
    
    this.type();
  }

  type() {
    const currentText = this.texts[this.textIndex];
    
    if (this.isDeleting) {
      this.element.textContent = currentText.substring(0, this.charIndex - 1);
      this.charIndex--;
    } else {
      this.element.textContent = currentText.substring(0, this.charIndex + 1);
      this.charIndex++;
    }

    let typeSpeed = this.speed;

    if (this.isDeleting) {
      typeSpeed /= 2;
    }

    if (!this.isDeleting && this.charIndex === currentText.length) {
      typeSpeed = 2000; // Pause at end
      this.isDeleting = true;
    } else if (this.isDeleting && this.charIndex === 0) {
      this.isDeleting = false;
      this.textIndex = (this.textIndex + 1) % this.texts.length;
      typeSpeed = 500; // Pause before next text
    }

    setTimeout(() => this.type(), typeSpeed);
  }
}

// ===== COUNTER ANIMATION =====

class CounterAnimation {
  constructor() {
    this.counters = document.querySelectorAll('.stat-number');
    this.animateCounters();
  }

  animateCounters() {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.animateCounter(entry.target);
          observer.unobserve(entry.target);
        }
      });
    });

    this.counters.forEach(counter => {
      observer.observe(counter);
    });
  }

  animateCounter(element) {
    const target = parseInt(element.dataset.target);
    const duration = 2000;
    const increment = target / (duration / 16);
    let current = 0;

    const timer = setInterval(() => {
      current += increment;
      element.textContent = Math.floor(current);

      if (current >= target) {
        element.textContent = target;
        clearInterval(timer);
      }
    }, 16);
  }
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Initialize particle system
  const canvas = document.getElementById('particle-canvas');
  if (canvas) {
    new ParticleSystem(canvas);
  }

  // Initialize terminal
  new InteractiveTerminal();

  // Initialize typewriter
  const typewriterElement = document.getElementById('typewriter-text');
  if (typewriterElement) {
    const roles = [
      'Full-Stack Developer',
      'Problem Solver',
      'Code Architect',
      'Innovation Driver',
      'Tech Enthusiast'
    ];
    new TypewriterEffect(typewriterElement, roles, 150);
  }

  // Initialize counters
  new CounterAnimation();
});
