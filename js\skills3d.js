// ===== 3D SKILLS VISUALIZATION =====

class Skills3D {
  constructor() {
    this.scene = document.getElementById('skills-scene');
    this.detailsPanel = document.getElementById('skill-details');
    this.currentCategory = 'frontend';
    this.skillSpheres = [];
    this.animationId = null;
    
    this.skillDescriptions = {
      'JavaScript': {
        description: 'Expert in modern JavaScript (ES6+), including async/await, modules, and advanced concepts.',
        level: 90,
        projects: ['E-Commerce Platform', 'Task Manager', 'Chat App']
      },
      'React': {
        description: 'Advanced React development with hooks, context, and modern patterns.',
        level: 85,
        projects: ['Task Manager', 'E-Commerce Platform']
      },
      'Vue.js': {
        description: 'Proficient in Vue.js ecosystem including Vuex and Vue Router.',
        level: 70,
        projects: ['Portfolio Website']
      },
      'Node.js': {
        description: 'Backend development with Node.js, Express, and various npm packages.',
        level: 85,
        projects: ['Blog API', 'Chat App', 'E-Commerce Platform']
      },
      'Python': {
        description: 'Full-stack Python development with Django and data processing libraries.',
        level: 80,
        projects: ['Data Analysis Tool', 'API Services']
      },
      'MongoDB': {
        description: 'NoSQL database design and optimization with MongoDB.',
        level: 75,
        projects: ['E-Commerce Platform', 'Blog API']
      }
    };
    
    this.init();
  }

  init() {
    this.setupEventListeners();
    this.renderSkills();
    this.startAnimation();
  }

  setupEventListeners() {
    // Category navigation
    document.querySelectorAll('.skill-nav-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.switchCategory(e.target.closest('.skill-nav-btn').dataset.category);
      });
    });

    // Window resize
    window.addEventListener('resize', () => {
      this.repositionSkills();
    });
  }

  switchCategory(category) {
    // Update active button
    document.querySelectorAll('.skill-nav-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-category="${category}"]`).classList.add('active');

    this.currentCategory = category;
    this.renderSkills();
  }

  renderSkills() {
    // Clear existing spheres
    this.scene.innerHTML = '';
    this.skillSpheres = [];

    let skillsToRender = [];

    if (this.currentCategory === 'all') {
      skillsToRender = [
        ...skillsData.frontend,
        ...skillsData.backend,
        ...skillsData.tools
      ];
    } else {
      skillsToRender = skillsData[this.currentCategory] || [];
    }

    // Create skill spheres
    skillsToRender.forEach((skill, index) => {
      this.createSkillSphere(skill, index);
    });

    this.repositionSkills();
  }

  createSkillSphere(skill, index) {
    const sphere = document.createElement('div');
    sphere.className = 'skill-sphere';
    sphere.dataset.skill = skill.name;
    
    // Create level ring
    const levelRing = document.createElement('div');
    levelRing.className = 'skill-level-ring';
    const levelPercentage = this.getLevelPercentage(skill.level);
    levelRing.style.borderTopColor = this.getLevelColor(levelPercentage);
    levelRing.style.transform = `rotate(${(levelPercentage / 100) * 360 - 90}deg)`;
    
    sphere.innerHTML = `
      <i class="${skill.icon} skill-icon"></i>
      <span class="skill-name">${skill.name}</span>
    `;
    
    sphere.appendChild(levelRing);
    
    // Add event listeners
    sphere.addEventListener('mouseenter', () => {
      this.showSkillDetails(skill);
      this.highlightSphere(sphere);
    });
    
    sphere.addEventListener('mouseleave', () => {
      this.resetSphereHighlight(sphere);
    });

    // Add random animation delay
    sphere.style.animationDelay = `${index * 0.2}s`;
    
    this.scene.appendChild(sphere);
    this.skillSpheres.push({ element: sphere, skill: skill, index: index });
  }

  repositionSkills() {
    const sceneRect = this.scene.getBoundingClientRect();
    const centerX = sceneRect.width / 2;
    const centerY = sceneRect.height / 2;
    const radius = Math.min(centerX, centerY) * 0.7;

    this.skillSpheres.forEach(({ element, index }) => {
      const angle = (index / this.skillSpheres.length) * 2 * Math.PI;
      const x = centerX + Math.cos(angle) * radius - 40; // 40 is half sphere width
      const y = centerY + Math.sin(angle) * radius - 40; // 40 is half sphere height
      
      element.style.left = `${x}px`;
      element.style.top = `${y}px`;
      
      // Add some randomness to make it more organic
      const randomX = (Math.random() - 0.5) * 50;
      const randomY = (Math.random() - 0.5) * 50;
      element.style.transform = `translate(${randomX}px, ${randomY}px)`;
    });
  }

  showSkillDetails(skill) {
    const details = this.skillDescriptions[skill.name] || {
      description: `Proficient in ${skill.name} with practical experience in various projects.`,
      level: this.getLevelPercentage(skill.level),
      projects: ['Various Projects']
    };

    const nameElement = this.detailsPanel.querySelector('.skill-detail-name');
    const levelFill = this.detailsPanel.querySelector('.level-fill');
    const levelText = this.detailsPanel.querySelector('.level-text');
    const description = this.detailsPanel.querySelector('.skill-detail-description');
    const projectTags = this.detailsPanel.querySelector('.project-tags');

    nameElement.textContent = skill.name;
    levelFill.style.width = `${details.level}%`;
    levelText.textContent = skill.level;
    description.textContent = details.description;

    // Update project tags
    projectTags.innerHTML = details.projects.map(project => 
      `<span class="project-tag">${project}</span>`
    ).join('');
  }

  highlightSphere(sphere) {
    // Dim other spheres
    this.skillSpheres.forEach(({ element }) => {
      if (element !== sphere) {
        element.style.opacity = '0.3';
        element.style.transform += ' scale(0.8)';
      }
    });
    
    // Highlight current sphere
    sphere.style.opacity = '1';
    sphere.style.zIndex = '100';
  }

  resetSphereHighlight(sphere) {
    // Reset all spheres
    this.skillSpheres.forEach(({ element }) => {
      element.style.opacity = '1';
      element.style.transform = element.style.transform.replace(' scale(0.8)', '');
      element.style.zIndex = 'auto';
    });
  }

  getLevelPercentage(level) {
    const levels = {
      'Beginner': 25,
      'Intermediate': 50,
      'Advanced': 75,
      'Expert': 90
    };
    return levels[level] || 50;
  }

  getLevelColor(percentage) {
    if (percentage >= 80) return '#10B981'; // Green
    if (percentage >= 60) return '#F59E0B'; // Yellow
    if (percentage >= 40) return '#EF4444'; // Red
    return '#6B7280'; // Gray
  }

  startAnimation() {
    const animate = () => {
      // Rotate skill spheres slowly
      this.skillSpheres.forEach(({ element, index }) => {
        const time = Date.now() * 0.001;
        const rotationSpeed = 0.5 + (index % 3) * 0.2;
        const currentTransform = element.style.transform || '';
        
        if (!element.matches(':hover')) {
          element.style.transform = currentTransform + ` rotateY(${time * rotationSpeed * 10}deg)`;
        }
      });

      this.animationId = requestAnimationFrame(animate);
    };

    animate();
  }

  destroy() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }
  }
}

// ===== SKILLS RADAR CHART (Alternative Visualization) =====

class SkillsRadarChart {
  constructor(canvas, skills) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d');
    this.skills = skills;
    this.center = { x: canvas.width / 2, y: canvas.height / 2 };
    this.radius = Math.min(canvas.width, canvas.height) / 2 - 50;
    
    this.draw();
  }

  draw() {
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    
    // Draw grid
    this.drawGrid();
    
    // Draw skill areas
    this.drawSkillArea();
    
    // Draw skill points
    this.drawSkillPoints();
    
    // Draw labels
    this.drawLabels();
  }

  drawGrid() {
    const levels = 5;
    this.ctx.strokeStyle = 'rgba(212, 175, 55, 0.2)';
    this.ctx.lineWidth = 1;

    // Draw concentric circles
    for (let i = 1; i <= levels; i++) {
      this.ctx.beginPath();
      this.ctx.arc(this.center.x, this.center.y, (this.radius / levels) * i, 0, Math.PI * 2);
      this.ctx.stroke();
    }

    // Draw radial lines
    const angleStep = (Math.PI * 2) / this.skills.length;
    for (let i = 0; i < this.skills.length; i++) {
      const angle = i * angleStep - Math.PI / 2;
      this.ctx.beginPath();
      this.ctx.moveTo(this.center.x, this.center.y);
      this.ctx.lineTo(
        this.center.x + Math.cos(angle) * this.radius,
        this.center.y + Math.sin(angle) * this.radius
      );
      this.ctx.stroke();
    }
  }

  drawSkillArea() {
    this.ctx.fillStyle = 'rgba(212, 175, 55, 0.2)';
    this.ctx.strokeStyle = 'rgba(212, 175, 55, 0.8)';
    this.ctx.lineWidth = 2;

    this.ctx.beginPath();
    const angleStep = (Math.PI * 2) / this.skills.length;
    
    this.skills.forEach((skill, index) => {
      const angle = index * angleStep - Math.PI / 2;
      const level = this.getLevelValue(skill.level) / 100;
      const x = this.center.x + Math.cos(angle) * this.radius * level;
      const y = this.center.y + Math.sin(angle) * this.radius * level;
      
      if (index === 0) {
        this.ctx.moveTo(x, y);
      } else {
        this.ctx.lineTo(x, y);
      }
    });
    
    this.ctx.closePath();
    this.ctx.fill();
    this.ctx.stroke();
  }

  drawSkillPoints() {
    const angleStep = (Math.PI * 2) / this.skills.length;
    
    this.skills.forEach((skill, index) => {
      const angle = index * angleStep - Math.PI / 2;
      const level = this.getLevelValue(skill.level) / 100;
      const x = this.center.x + Math.cos(angle) * this.radius * level;
      const y = this.center.y + Math.sin(angle) * this.radius * level;
      
      this.ctx.fillStyle = '#D4AF37';
      this.ctx.beginPath();
      this.ctx.arc(x, y, 4, 0, Math.PI * 2);
      this.ctx.fill();
    });
  }

  drawLabels() {
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.font = '12px Inter';
    this.ctx.textAlign = 'center';
    
    const angleStep = (Math.PI * 2) / this.skills.length;
    
    this.skills.forEach((skill, index) => {
      const angle = index * angleStep - Math.PI / 2;
      const labelRadius = this.radius + 20;
      const x = this.center.x + Math.cos(angle) * labelRadius;
      const y = this.center.y + Math.sin(angle) * labelRadius;
      
      this.ctx.fillText(skill.name, x, y);
    });
  }

  getLevelValue(level) {
    const levels = {
      'Beginner': 25,
      'Intermediate': 50,
      'Advanced': 75,
      'Expert': 90
    };
    return levels[level] || 50;
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Check if 3D is supported
  const supportsCSS3D = 'transform-style' in document.documentElement.style;
  
  if (supportsCSS3D) {
    new Skills3D();
  } else {
    // Fallback to traditional grid
    document.querySelector('.skills-grid-fallback').style.display = 'block';
    document.querySelector('.skills-3d-container').style.display = 'none';
  }
});
