# Deployment Guide

This guide will help you deploy your portfolio website to various hosting platforms.

## 🚀 Quick Deployment Options

### 1. Netlify (Recommended)

**Option A: Drag & Drop**
1. Go to [netlify.com](https://netlify.com)
2. Sign up for a free account
3. Drag and drop your portfolio folder to the deploy area
4. Your site will be live instantly with a random URL
5. You can customize the URL in site settings

**Option B: Git Integration**
1. Push your portfolio to a GitHub repository
2. Connect your GitHub account to Netlify
3. Select your repository and deploy
4. Automatic deployments on every push

### 2. Vercel

1. Go to [vercel.com](https://vercel.com)
2. Sign up with GitHub
3. Import your portfolio repository
4. Deploy with zero configuration
5. Get automatic HTTPS and global CDN

### 3. GitHub Pages

1. Push your code to a GitHub repository
2. Go to repository Settings > Pages
3. Select source branch (usually `main`)
4. Your site will be available at `username.github.io/repository-name`

### 4. Traditional Web Hosting

1. Purchase hosting from providers like:
   - Bluehost
   - SiteGround
   - HostGator
   - GoDaddy
2. Upload files via FTP/cPanel File Manager
3. Point your domain to the hosting

## 🔧 Pre-Deployment Checklist

### Content Updates
- [ ] Update personal information in `index.html`
- [ ] Replace placeholder data in `js/data.js`
- [ ] Add your actual projects and GitHub links
- [ ] Update contact information
- [ ] Add your resume PDF to `assets/`
- [ ] Replace placeholder images

### Technical Checks
- [ ] Test all navigation links
- [ ] Verify all external links work
- [ ] Check responsive design on different devices
- [ ] Test contact form (if using a form service)
- [ ] Optimize images for web
- [ ] Add your favicon to `assets/`

### SEO Optimization
- [ ] Update meta descriptions
- [ ] Add Open Graph image
- [ ] Verify all meta tags
- [ ] Test social media sharing
- [ ] Submit to Google Search Console

## 🌐 Custom Domain Setup

### Netlify
1. Go to Site Settings > Domain Management
2. Add your custom domain
3. Configure DNS records with your domain provider
4. SSL certificate is automatically provided

### Vercel
1. Go to Project Settings > Domains
2. Add your custom domain
3. Configure DNS records
4. SSL is automatically configured

### GitHub Pages
1. Add a `CNAME` file with your domain
2. Configure DNS with your domain provider
3. Enable HTTPS in repository settings

## 📊 Analytics Setup

### Google Analytics
1. Create a Google Analytics account
2. Get your tracking ID
3. Add the tracking code to your `index.html`:

```html
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_TRACKING_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_TRACKING_ID');
</script>
```

## 📧 Contact Form Integration

Since this is a static site, you'll need a service for the contact form:

### Netlify Forms (Free)
1. Add `netlify` attribute to your form:
```html
<form name="contact" method="POST" data-netlify="true">
```

### Formspree (Free tier available)
1. Sign up at [formspree.io](https://formspree.io)
2. Update form action:
```html
<form action="https://formspree.io/f/YOUR_FORM_ID" method="POST">
```

### EmailJS (Free tier available)
1. Sign up at [emailjs.com](https://emailjs.com)
2. Add EmailJS SDK and configure
3. Update the contact form handler in `js/main.js`

## 🔍 Testing Your Deployment

### Performance Testing
- [Google PageSpeed Insights](https://pagespeed.web.dev/)
- [GTmetrix](https://gtmetrix.com/)
- [WebPageTest](https://www.webpagetest.org/)

### Accessibility Testing
- [WAVE Web Accessibility Evaluator](https://wave.webaim.org/)
- [axe DevTools](https://www.deque.com/axe/devtools/)

### Cross-Browser Testing
- Test on Chrome, Firefox, Safari, Edge
- Test on mobile devices (iOS Safari, Chrome Mobile)
- Use [BrowserStack](https://www.browserstack.com/) for comprehensive testing

## 🚨 Common Issues & Solutions

### Images Not Loading
- Check file paths are correct
- Ensure images are in the `assets/` directory
- Verify image file extensions match the HTML references

### CSS/JS Not Loading
- Check file paths in `index.html`
- Ensure files are uploaded to the correct directories
- Clear browser cache

### Contact Form Not Working
- Verify form service integration
- Check form action URL
- Test form submission

### Mobile Layout Issues
- Test responsive design on actual devices
- Use browser developer tools for mobile simulation
- Check viewport meta tag is present

## 📈 Post-Deployment

### SEO
1. Submit sitemap to Google Search Console
2. Create social media profiles with portfolio link
3. Add portfolio link to your resume
4. Share on professional networks

### Maintenance
- Regularly update project information
- Add new projects as you complete them
- Keep dependencies updated
- Monitor site performance
- Backup your code regularly

## 🎯 Next Steps

1. Deploy your site using one of the methods above
2. Test thoroughly on different devices and browsers
3. Share your portfolio with potential employers
4. Continue adding projects and updating content
5. Consider adding a blog section for technical articles

---

**Need Help?** If you encounter issues during deployment, check the documentation for your chosen platform or reach out for support.
