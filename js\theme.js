// ===== ADVANCED THEME SYSTEM =====

class ThemeManager {
  constructor() {
    this.currentTheme = 'light';
    this.toggleButton = document.getElementById('theme-toggle');
    this.prefersDark = window.matchMedia('(prefers-color-scheme: dark)');
    
    this.init();
  }

  init() {
    // Load saved theme or use system preference
    this.loadTheme();
    
    // Setup event listeners
    this.setupEventListeners();
    
    // Apply initial theme
    this.applyTheme(this.currentTheme, false);
  }

  loadTheme() {
    const savedTheme = localStorage.getItem('portfolio-theme');
    
    if (savedTheme) {
      this.currentTheme = savedTheme;
    } else {
      // Use system preference
      this.currentTheme = this.prefersDark.matches ? 'dark' : 'light';
    }
  }

  setupEventListeners() {
    // Theme toggle button
    if (this.toggleButton) {
      this.toggleButton.addEventListener('click', () => {
        this.toggleTheme();
      });
    }

    // Listen for system theme changes
    this.prefersDark.addEventListener('change', (e) => {
      if (!localStorage.getItem('portfolio-theme')) {
        this.currentTheme = e.matches ? 'dark' : 'light';
        this.applyTheme(this.currentTheme, true);
      }
    });

    // Keyboard shortcut (Ctrl/Cmd + Shift + T)
    document.addEventListener('keydown', (e) => {
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'T') {
        e.preventDefault();
        this.toggleTheme();
      }
    });
  }

  toggleTheme() {
    this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
    this.applyTheme(this.currentTheme, true);
    this.saveTheme();
  }

  applyTheme(theme, animate = true) {
    const root = document.documentElement;
    
    if (animate) {
      // Add transition class for smooth theme change
      root.classList.add('theme-transition');
      
      // Remove transition class after animation
      setTimeout(() => {
        root.classList.remove('theme-transition');
      }, 300);
    }

    // Apply theme attribute
    root.setAttribute('data-theme', theme);
    
    // Update meta theme-color for mobile browsers
    this.updateMetaThemeColor(theme);
    
    // Trigger custom event for other components
    window.dispatchEvent(new CustomEvent('themeChanged', {
      detail: { theme, previousTheme: this.currentTheme }
    }));
  }

  updateMetaThemeColor(theme) {
    let metaThemeColor = document.querySelector('meta[name="theme-color"]');
    
    if (!metaThemeColor) {
      metaThemeColor = document.createElement('meta');
      metaThemeColor.name = 'theme-color';
      document.head.appendChild(metaThemeColor);
    }

    const colors = {
      light: '#FFFFFF',
      dark: '#111827'
    };

    metaThemeColor.content = colors[theme];
  }

  saveTheme() {
    localStorage.setItem('portfolio-theme', this.currentTheme);
  }

  getCurrentTheme() {
    return this.currentTheme;
  }

  // Method to programmatically set theme
  setTheme(theme) {
    if (['light', 'dark'].includes(theme)) {
      this.currentTheme = theme;
      this.applyTheme(theme, true);
      this.saveTheme();
    }
  }
}

// ===== ENHANCED BACKGROUND EFFECTS =====

class BackgroundEffects {
  constructor() {
    this.effects = [];
    this.isActive = true;
    this.currentTheme = 'light';
    
    this.init();
  }

  init() {
    this.createFloatingElements();
    this.setupParallaxElements();
    this.setupMouseTracker();
    this.setupScrollEffects();
    
    // Listen for theme changes
    window.addEventListener('themeChanged', (e) => {
      this.currentTheme = e.detail.theme;
      this.updateEffectsForTheme();
    });
  }

  createFloatingElements() {
    const container = document.createElement('div');
    container.className = 'floating-bg-elements';
    container.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
      overflow: hidden;
    `;

    // Create floating geometric shapes
    for (let i = 0; i < 8; i++) {
      const element = document.createElement('div');
      element.className = 'floating-element';
      element.style.cssText = `
        position: absolute;
        width: ${20 + Math.random() * 40}px;
        height: ${20 + Math.random() * 40}px;
        background: rgba(212, 175, 55, ${0.1 + Math.random() * 0.2});
        border-radius: ${Math.random() > 0.5 ? '50%' : '0'};
        left: ${Math.random() * 100}%;
        top: ${Math.random() * 100}%;
        animation: floatRandom ${10 + Math.random() * 20}s infinite linear;
        animation-delay: ${Math.random() * 10}s;
      `;
      
      container.appendChild(element);
    }

    document.body.appendChild(container);
    this.effects.push(container);
  }

  setupParallaxElements() {
    const parallaxElements = document.querySelectorAll('[data-parallax]');
    
    if (parallaxElements.length === 0) return;

    let ticking = false;

    const updateParallax = () => {
      const scrollY = window.pageYOffset;

      parallaxElements.forEach(element => {
        const speed = parseFloat(element.dataset.parallax) || 0.5;
        const yPos = -(scrollY * speed);
        element.style.transform = `translateY(${yPos}px)`;
      });

      ticking = false;
    };

    const requestParallaxUpdate = () => {
      if (!ticking && this.isActive) {
        requestAnimationFrame(updateParallax);
        ticking = true;
      }
    };

    window.addEventListener('scroll', requestParallaxUpdate, { passive: true });
  }

  setupMouseTracker() {
    const tracker = document.createElement('div');
    tracker.className = 'mouse-tracker';
    tracker.style.cssText = `
      position: fixed;
      width: 200px;
      height: 200px;
      background: radial-gradient(circle, rgba(212, 175, 55, 0.1) 0%, transparent 70%);
      border-radius: 50%;
      pointer-events: none;
      z-index: -1;
      transition: transform 0.1s ease;
      opacity: 0;
    `;

    document.body.appendChild(tracker);

    let mouseX = 0;
    let mouseY = 0;
    let isMoving = false;

    const updateTracker = () => {
      tracker.style.left = (mouseX - 100) + 'px';
      tracker.style.top = (mouseY - 100) + 'px';
    };

    document.addEventListener('mousemove', (e) => {
      mouseX = e.clientX;
      mouseY = e.clientY;
      
      if (!isMoving) {
        tracker.style.opacity = '1';
        isMoving = true;
      }
      
      updateTracker();
      
      // Hide tracker after mouse stops
      clearTimeout(this.mouseTimeout);
      this.mouseTimeout = setTimeout(() => {
        tracker.style.opacity = '0';
        isMoving = false;
      }, 2000);
    });

    this.effects.push(tracker);
  }

  setupScrollEffects() {
    let lastScrollY = window.pageYOffset;
    let ticking = false;

    const updateScrollEffects = () => {
      const scrollY = window.pageYOffset;
      const scrollDirection = scrollY > lastScrollY ? 'down' : 'up';
      const scrollProgress = scrollY / (document.documentElement.scrollHeight - window.innerHeight);

      // Update CSS custom property for scroll progress
      document.documentElement.style.setProperty('--scroll-progress', scrollProgress);

      // Add scroll direction class to body
      document.body.classList.remove('scroll-up', 'scroll-down');
      document.body.classList.add(`scroll-${scrollDirection}`);

      lastScrollY = scrollY;
      ticking = false;
    };

    const requestScrollUpdate = () => {
      if (!ticking && this.isActive) {
        requestAnimationFrame(updateScrollEffects);
        ticking = true;
      }
    };

    window.addEventListener('scroll', requestScrollUpdate, { passive: true });
  }

  updateEffectsForTheme() {
    const floatingElements = document.querySelectorAll('.floating-element');
    const opacity = this.currentTheme === 'dark' ? 0.3 : 0.1;
    
    floatingElements.forEach(element => {
      element.style.background = `rgba(212, 175, 55, ${opacity + Math.random() * 0.2})`;
    });
  }

  toggleEffects() {
    this.isActive = !this.isActive;
    
    this.effects.forEach(effect => {
      effect.style.display = this.isActive ? 'block' : 'none';
    });
  }

  destroy() {
    this.effects.forEach(effect => {
      if (effect.parentNode) {
        effect.parentNode.removeChild(effect);
      }
    });
    this.effects = [];
  }
}

// Add floating animation CSS
const floatingCSS = `
  .theme-transition * {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
  }

  @keyframes floatRandom {
    0% {
      transform: translateY(0px) rotate(0deg);
    }
    25% {
      transform: translateY(-20px) rotate(90deg);
    }
    50% {
      transform: translateY(0px) rotate(180deg);
    }
    75% {
      transform: translateY(-10px) rotate(270deg);
    }
    100% {
      transform: translateY(0px) rotate(360deg);
    }
  }

  /* Scroll-based animations */
  .scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s ease;
  }

  .scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
  }
`;

// Add CSS to document
const styleSheet = document.createElement('style');
styleSheet.textContent = floatingCSS;
document.head.appendChild(styleSheet);

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.themeManager = new ThemeManager();
  window.backgroundEffects = new BackgroundEffects();
});
